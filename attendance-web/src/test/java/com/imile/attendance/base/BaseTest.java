package com.imile.attendance.base;

import com.imile.attendance.AttendanceApplication;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
public class BaseTest {

    @Before
    public void init() {
        UserContext userContext = new UserContext();
        userContext.setUserCode("2103429201");
        userContext.setUserName("test-martin");
        RequestInfoHolder.setLoginInfo(userContext);

        RequestInfoHolder.getUserCode();
        log.info("current userCode:{}", RequestInfoHolder.getUserCode());
    }
}
