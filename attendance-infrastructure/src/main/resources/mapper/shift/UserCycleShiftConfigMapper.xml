<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.shift.mapper.UserCycleShiftConfigMapper">
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.shift.model.UserCycleShiftConfigDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="punch_class_config_id" jdbcType="BIGINT" property="punchClassConfigId" />
        <result column="cycle_period" jdbcType="TINYINT" property="cyclePeriod" />
        <result column="day_shift_info" jdbcType="VARCHAR" property="dayShiftInfo" />
        <result column="effect_date" jdbcType="TIMESTAMP" property="effectDate" />
        <result column="expire_date" jdbcType="TIMESTAMP" property="expireDate" />
        <result column="is_latest" jdbcType="TINYINT" property="isLatest" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, punch_class_config_id, cycle_period, day_shift_info, effect_date, expire_date,
    is_latest, is_delete, record_version, create_date, create_user_code, create_user_name,
    last_upd_date, last_upd_user_code, last_upd_user_name
    </sql>


</mapper>
