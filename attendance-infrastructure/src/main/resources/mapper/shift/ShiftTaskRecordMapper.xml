<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.shift.mapper.ShiftTaskRecordMapper">
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.shift.model.ShiftTaskRecordDO">
        <result column="task_flag" jdbcType="VARCHAR" property="taskFlag" />
        <result column="shift_type" jdbcType="VARCHAR" property="shiftType" />
        <result column="shift_source" jdbcType="VARCHAR" property="shiftSource" />
        <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
        <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="cost_second" jdbcType="INTEGER" property="costSecond" />
        <result column="error_message" jdbcType="VARCHAR" property="errorMessage" />
        <result column="priority" jdbcType="INTEGER" property="priority" />
        <result column="executed_count" jdbcType="INTEGER" property="executedCount" />
        <result column="total_count" jdbcType="INTEGER" property="totalCount" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    </resultMap>

    <sql id="Base_Column_List">
        id, task_flag, shift_type, shift_source, start_date, end_date, status, cost_second,
    error_message, priority, executed_count, total_count, is_delete, record_version,
    create_date, create_user_code, create_user_name, last_upd_date, last_upd_user_code,
    last_upd_user_name
    </sql>

</mapper>
