<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.rule.mapper.PunchClassConfigRangeMapper">

    <select id="listClassRangeApplyUser"
            parameterType="com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery"
            resultType="com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO">
        SELECT u.*
        FROM punch_class_config_range ccr
        JOIN user_info u on ccr.biz_id = u.id
        WHERE  u.is_driver = 0
        AND u.work_status = 'ON_JOB'
        AND u.`status`= 'ACTIVE'
        AND u.is_delete = 0
        AND ccr.is_delete = 0
        AND ccr.is_latest = 1
        <if test="userIds != null and userIds.size() > 0">
            AND u.id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
            AND u.dept_id IN
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="classNature != null and classNature != ''">
            AND u.class_nature = #{classNature}
        </if>
        <if test="classId != null">
            AND ccr.rule_config_id = #{classId}
        </if>
        <if test="rangeTypeList != null and rangeTypeList.size() > 0">
            AND ccr.range_type IN
            <foreach collection="rangeTypeList" item="rangeType" open="(" separator="," close=")">
                #{rangeType}
            </foreach>
        </if>
        <if test="codeOrNameLike != null and codeOrNameLike != ''">
            AND (u.user_code LIKE CONCAT('%', #{codeOrNameLike}, '%')
            OR u.user_name LIKE CONCAT('%', #{codeOrNameLike}, '%'))
        </if>
    </select>

</mapper>
