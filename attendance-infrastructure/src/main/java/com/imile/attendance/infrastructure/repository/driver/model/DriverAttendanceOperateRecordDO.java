package com.imile.attendance.infrastructure.repository.driver.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
@ApiModel(description = "司机考勤操作记录表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("driver_attendance_operate_record")
public class DriverAttendanceOperateRecordDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String country;

    /**
     * 操作类型：1：修改考勤数据、2：导出 ...
     */
    @ApiModelProperty(value = "操作类型：1：修改考勤数据、2：导出 ...")
    private Integer operationType;

    /**
     * 操作内容：被修改人：xxxD1234，日报 or 月报
     */
    @ApiModelProperty(value = "操作内容：被修改人：xxxD1234，日报 or 月报")
    private String operationContent;

    /**
     * 操作内容（英文版）
     */
    @ApiModelProperty(value = "操作内容（英文版）")
    private String operationContentEn;
}

