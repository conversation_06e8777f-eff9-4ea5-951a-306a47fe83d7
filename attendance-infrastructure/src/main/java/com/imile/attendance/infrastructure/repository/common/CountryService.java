package com.imile.attendance.infrastructure.repository.common;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.imile.attendance.hermes.RpcHermesCountryClient;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.hermes.support.RpcHermesCountrySupport;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 国家配置信息
 */
@Component
public class CountryService {

    @Resource
    private RpcHermesCountryClient rpcHermesCountryClient;
    @Resource
    private RpcHermesCountrySupport rpcHermesCountrySupport;


    /**
     * 拉取所有业务国家配置
     */
    public List<CountryDTO> listAllCountry(){
        return rpcHermesCountrySupport.listAllCountry();
    }

    /**
     * 拉取所有业务国家配置
     */
    public List<CountryConfigDTO> queryAllCountryConfigList(){
        return rpcHermesCountryClient.queryAllCountryConfigList();
    }


    /**
     * 根据国家编码拉取国家配置
     */
    public CountryDTO queryCountry(String countryCode){
        return rpcHermesCountrySupport.queryCountry(countryCode);
    }

    /**
     * 获取国家配置
     */
    public CountryConfigDTO queryCountryConfig(CountryApiQuery query){
        return rpcHermesCountryClient.queryCountryConfig(query);
    }

    /**
     * 获取国家配置
     */
    public List<CountryConfigDTO> selectCountryConfigList(CountryApiQuery query){
        return rpcHermesCountryClient.queryCountryConfigList(query);
    }
}
