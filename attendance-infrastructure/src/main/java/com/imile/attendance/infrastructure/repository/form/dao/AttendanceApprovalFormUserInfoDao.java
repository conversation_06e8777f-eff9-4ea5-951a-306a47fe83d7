package com.imile.attendance.infrastructure.repository.form.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.form.dto.OverTimeApprovalListDTO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormUserInfoDO;
import com.imile.attendance.infrastructure.repository.form.query.ApprovalFormUserInfoQuery;
import com.imile.attendance.infrastructure.repository.form.query.OverTimeListQuery;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
public interface AttendanceApprovalFormUserInfoDao extends IService<AttendanceApprovalFormUserInfoDO> {

    List<AttendanceApprovalFormUserInfoDO> selectByCondition(ApprovalFormUserInfoQuery approvalFormUserInfoQuery);

    List<OverTimeApprovalListDTO> selectListByCondition(OverTimeListQuery overTimeListQuery);
}

