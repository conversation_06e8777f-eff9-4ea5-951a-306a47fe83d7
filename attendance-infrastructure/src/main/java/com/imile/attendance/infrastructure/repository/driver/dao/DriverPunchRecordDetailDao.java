package com.imile.attendance.infrastructure.repository.driver.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.driver.model.DriverPunchRecordDetailDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordDetailInfoQuery;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
public interface DriverPunchRecordDetailDao extends IService<DriverPunchRecordDetailDO> {


    List<DriverPunchRecordDetailDO> queryDriverPunchRecordDetailByCondition(DriverPunchRecordDetailInfoQuery driverPunchRecordDetailInfoQuery);

    List<DriverPunchRecordDetailDO> listByPage(int currentPage, int pageSize);


}

