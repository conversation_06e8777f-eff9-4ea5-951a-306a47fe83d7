package com.imile.attendance.infrastructure.repository.rule.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigExportDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchClassConfigQuery;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Mapper
@Repository
public interface PunchClassConfigMapper extends BaseMapper<PunchClassConfigDO> {

    List<PunchClassConfigDO> pageQuery(PunchClassConfigQuery query);

    List<PunchClassConfigExportDTO> export(PunchClassConfigQuery query);
}
