package com.imile.attendance.infrastructure.repository.employee.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveRecordConditionQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveRecordQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
public interface UserLeaveRecordDao extends IService<UserLeaveRecordDO> {

    /**
     * 查询用户请假流水表
     *
     * @param query
     * @return
     */
    List<UserLeaveRecordDO> selectUserLeaveDetail(UserLeaveRecordQuery query);

    List<UserLeaveRecordDO> selectRecordByUserIdList(List<Long> userIdList);


    List<UserLeaveRecordDO> selectUserLeaveRecordByCondition(UserLeaveRecordConditionQuery query);

    /**
     * 查询用户在该天的所有请假记录
     */
    List<UserLeaveRecordDO> selectRecordByDayId(Long userId, String usrCode, Long dayId);

    /**
     * 根据用户id查询用户指定假期操作记录
     *
     * @param userId
     * @return
     */
    List<UserLeaveRecordDO> selectRecordByUserId(Long userId, String leaveType);

}
