package com.imile.attendance.infrastructure.repository.driver.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
@ApiModel(description = "司机每日考勤信息表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("driver_attendance_detail")
public class DriverAttendanceDetailDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 司机账号
     */
    @ApiModelProperty(value = "司机账号")
    private String userCode;

    /**
     * 年
     */
    @ApiModelProperty(value = "年份")
    private Integer year;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private Integer month;

    /**
     * 日
     */
    @ApiModelProperty(value = "日期")
    private Integer day;

    /**
     * 日期时间
     */
    @ApiModelProperty(value = "日期时间")
    private Date date;

    /**
     * day_id 示例：20240124
     */
    @ApiModelProperty(value = "day_id 示例：20240124")
    private Long dayId;

    /**
     * 出勤类型: 1：P 出勤，2：A 缺勤，3：L 请假 ...
     */
    @ApiModelProperty(value = "出勤类型: 1：P 出勤，2：A 缺勤，3：L 请假 ...")
    private Integer attendanceType;

    /**
     * 司机签收次数
     */
    @ApiModelProperty(value = "司机签收次数")
    private Long dldNumber;

    /**
     * 轨迹打卡次数
     */
    @ApiModelProperty(value = "轨迹打卡次数")
    private Long locusNumber;

    /**
     * 最近操作时间：取司机打卡记录表某人最近的操作时间
     */
    @ApiModelProperty(value = "最近操作时间：取司机打卡记录表某人最近的操作时间")
    private Date lastOperatingTime;
}

