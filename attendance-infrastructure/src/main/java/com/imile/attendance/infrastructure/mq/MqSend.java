package com.imile.attendance.infrastructure.mq;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description 考勤mq发送消息类
 */
@Slf4j
@Component
public class MqSend {

    @Resource
    private DefaultMQProducer defaultMqProducer;

    @Value("${rocket.mq.attendance.topic}")
    private String attendanceProducerTopic;


    public void sendMessage(String topic, String tag, String key, Object object) {
        try {
            SendResult sendResult = defaultMqProducer.send(new Message(topic, tag, key, JSON.toJSONString(object).getBytes()));
            log.info("MQ发送消息结果:{}", JSON.toJSONString(sendResult));
            if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
                //失败消息落库
                log.info("MQ发送消息失败");
            }
        } catch (Exception e) {
            log.error("MQ发送消息出现异常, e:{}", JSON.toJSONString(e));
        }
    }

    public void sendMessageWithDefaultHrTopic(String tag, String key, Object object) {
        sendMessage(attendanceProducerTopic, tag, key, object);
    }

    public void send(String topic, String tags, String key, String body) {
        try {
            log.info("sendMessage | topic={},tags={},key={},body={}", topic, tags, key, body);
            Message message = new Message(topic, tags, key, body.getBytes(StandardCharsets.UTF_8));
            SendResult sendResult = defaultMqProducer.send(message, (mqList, message1, arg) -> {
                int hashCode = Math.abs(key.hashCode());
                int index = hashCode % mqList.size();
                return mqList.get(index);
            }, key);
            log.info("sendMessage | sendResult={}", sendResult);
        } catch (Exception e) {
            log.error("sendMessage | is Exception", e);
        }
    }
}
