package com.imile.attendance.infrastructure.repository.rule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PunchClassConfigSelectDTO implements Serializable {

    /**
     * 班次ID
     */
    private Long id;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次时段信息
     */
    private List<PunchClassItemConfigDTO> classItemConfigDTOList;


    public static PunchClassConfigSelectDTO of(Long id, String className) {
        return PunchClassConfigSelectDTO.builder()
                .id(id)
                .className(className)
                .build();
    }
}
