package com.imile.attendance.infrastructure.repository.rule.query;

import com.imile.attendance.query.ResourceQuery;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> chen
 * @Date 2025/4/10 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PunchConfigQuery extends ResourceQuery {

    /**
     * 打卡规则id列表
     */
    private List<Long> punchConfigIds;

    /**
     * 国家
     */
    private String country;

    /**
     * 打卡规则类型
     */
    private String punchConfigType;

    /**
     * 打卡规则编码
     */
    private Set<String> punchConfigNos;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 部门
     */
    private Long deptId;

    /**
     * 部门列表
     */
    private List<Long> deptIds;

    /**
     * 状态
     */
    private String status;

    /**
     * 国家列表
     */
    private List<String> countryList;

    /**
     * 用户id列表
     */
    private List<Long> userIdList;

    /**
     * 是否分页导出
     */
    private Boolean arePageExport = false;
}
