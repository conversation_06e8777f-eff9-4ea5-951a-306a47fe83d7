package com.imile.attendance.infrastructure.idwork;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.enums.ApprovalNoPrefixEnum;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.exception.BaseBusinessException;
import com.imile.common.exception.BusinessException;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.idwork.IdWorkerUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.function.Function;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 生成ID和业务单据编码工具类
 */
@Component
@Slf4j
public class IdWorkUtils {

    @Resource
    private ImileRedisClient imileRedisClient;

    public static final String LOCK_KEY = "HRMS:ID_WORKER:LOCK:";
    public static final String KEY = "HRMS:ID_WORKER:";
    public static final String CONFIG_NO_KEY = "CONFIG_NO";
    public static final String SEPARATOR = ":";

    /**
     * 过期时间，秒
     */
    public static final Long EXPIRE_TIME = 3 * 60L;

    /**
     * 25个小时
     */
    public static final Long ATTENDANCE_CONFIG_EXPIRE_TIME = 25 * 60 * 60L;

    public Long nextId() {
        return IdWorkerUtil.getId();
    }


    /**
     * 生成带前缀的编码，形如：K20211217001
     *
     * @param approvalNoPrefixEnum
     * @return
     */
    public String nextNo(ApprovalNoPrefixEnum approvalNoPrefixEnum) {
        //输出 ==> K20211217001
        Function<ApprovalNoPrefixEnum, String> nextNoFunc = (idType) -> {
            String dayId = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);
            String cacheKey = KEY + CONFIG_NO_KEY + SEPARATOR + idType.getPrefix() + SEPARATOR + dayId;
            Object value = imileRedisClient.get(cacheKey);

            Long no = value == null ? 1 : Long.parseLong(String.valueOf(value)) + 1;
            //若保存成功，
            imileRedisClient.set(cacheKey, no, ATTENDANCE_CONFIG_EXPIRE_TIME);
            return approvalNoPrefixEnum.getPrefix() + (Long.parseLong(dayId) * 100000 + no);
        };
        return next(nextNoFunc, approvalNoPrefixEnum, CONFIG_NO_KEY + SEPARATOR + approvalNoPrefixEnum.getPrefix());
    }

    private <R, T> R next(Function<T, R> nextNoFunc, T typeEnum, String key) {
        try {
            //锁住保证幂等操作，  最多等待3秒
            boolean lockKeySuccess = imileRedisClient.tryLock(LOCK_KEY + key, 3, 10);
            //获取失败则抛出异常
            if (!lockKeySuccess) {
                throw BusinessException.get("", "");
            }
            return nextNoFunc.apply(typeEnum);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("interrupted error", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } catch (BaseBusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("get max value  fail", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            imileRedisClient.unlock(LOCK_KEY + key);
        }
    }
}
