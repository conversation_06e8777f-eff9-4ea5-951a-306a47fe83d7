package com.imile.attendance.infrastructure.excel.paramFill;

import com.alibaba.fastjson.JSONObject;
import com.imile.attendance.annon.ExportParamFill;
import com.imile.common.query.BaseQuery;
import lombok.experimental.FieldNameConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * <AUTHOR> chen
 * @Date 2024/12/3 
 * @Description
 */
@Slf4j
@Component
public class IpepParameterFillResolver extends AbstractParameterFillResolver{


    @Override
    public void doResolve(BaseQuery query, HttpServletRequest request) {
        Optional.ofNullable(request.getParameterMap().get(ExcelCallBackParam.Fields.pageNum))
                .ifPresent(args -> this.fill(query::setCurrentPage, value(args)));
        Optional.ofNullable(request.getParameterMap().get(ExcelCallBackParam.Fields.pageSize))
                .ifPresent(args -> this.fill(query::setShowCount, value(args)));
        Optional.ofNullable(request.getParameterMap().get(ExcelCallBackParam.Fields.totalCount))
                .ifPresent(args -> this.fill(query::setTotalResult, value(args)));
        //是否查询count
        Boolean isCount = (query.getCurrentPage() == 1);
        query.setCount(isCount);
        log.info("setExcelCallBackParam | query={}", query);
        query.setPageEnabled(true);
    }

    public <T> void fill(Consumer<T> setter, T value) {
        setter.accept(value);
    }

    public Integer value(String[] args) {
        if (args == null || args.length < 1) {
            return null;
        }
        try {
            return Integer.parseInt(args[0]);
        } catch (NumberFormatException e) {
            log.info("The first arg is not number, {}", JSONObject.toJSONString(args));
            return null;
        }
    }

    @Override
    public ExportParamFill.Source support() {
        return null;
    }


    @FieldNameConstants
    public static class ExcelCallBackParam {
        /**
         * 任务编码
         */
        private String jobId;
        /**
         * 页面数据
         */
        private String pageData;
        /**
         * 页面大小
         */
        private Integer pageSize;
        /**
         * 页码
         */
        private Integer pageNum;
        /**
         * 偏移号
         */
        private String scrollId;
        /**
         * 总数
         */
        private Integer totalCount;
        /**
         * 输出数
         */
        private Integer outCount;
        /**
         * 输出文件路径
         */
        private String outFilePath;
        /**
         * 用户编号
         */
        private Long userId;
        /**
         * 表头
         */
        private String titles;
        /**
         * 任务类型
         */
        private String excelJobType;
        /**
         * 执行策略 1仅校验 2落库
         */
        private Integer mode;

        /**
         * 页内最后一条记录ID
         */
        private Long pageEndRowId = null;
        /**
         * 页内第一条记录ID
         */
        private Long pageStartRowId = null;
        /**
         * 上次的页码
         */
        private Integer prePage = null;
        /**
         * 最后一页
         */
        private Boolean lastPageFlag = false;
    }
}
