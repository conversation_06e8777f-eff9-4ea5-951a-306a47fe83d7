package com.imile.attendance.infrastructure.repository.vacation.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveItemConfigDao;
import com.imile.attendance.infrastructure.repository.vacation.mapper.CompanyLeaveItemConfigMapper;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveItemConfigDO;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description 假期阶段范围 数据库操作实现类
 */
@Service
public class CompanyLeaveItemConfigDaoImpl extends ServiceImpl<CompanyLeaveItemConfigMapper, CompanyLeaveItemConfigDO> implements CompanyLeaveItemConfigDao {

    @Override
    public List<CompanyLeaveItemConfigDO> selectItemByConfigId(List<Long> leaveConfigIds) {
        if (CollectionUtils.isEmpty(leaveConfigIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CompanyLeaveItemConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CompanyLeaveItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(CompanyLeaveItemConfigDO::getLeaveId, leaveConfigIds);
        return list(queryWrapper);
    }
}
