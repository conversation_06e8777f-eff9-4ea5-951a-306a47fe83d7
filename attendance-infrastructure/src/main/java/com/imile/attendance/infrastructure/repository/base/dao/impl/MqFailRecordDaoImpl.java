package com.imile.attendance.infrastructure.repository.base.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.base.dao.MqFailRecordDao;
import com.imile.attendance.infrastructure.repository.base.mapper.MqFailRecordMapper;
import com.imile.attendance.infrastructure.repository.base.model.MqFailRecordDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/4/8 
 * @Description
 */
@Component
@RequiredArgsConstructor
public class MqFailRecordDaoImpl extends ServiceImpl<MqFailRecordMapper, MqFailRecordDO> implements MqFailRecordDao {

    @Override
    public MqFailRecordDO getByMsgId(String msgId) {
        if (StringUtils.isEmpty(msgId)) {
            return null;
        }
        LambdaQueryWrapper<MqFailRecordDO> queryWrapper = Wrappers.lambdaQuery(MqFailRecordDO.class);
        queryWrapper.eq(MqFailRecordDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(MqFailRecordDO::getMsgId, msgId);
        return this.getOne(queryWrapper);
    }
}
