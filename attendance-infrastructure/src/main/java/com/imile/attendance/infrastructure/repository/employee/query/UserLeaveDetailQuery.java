package com.imile.attendance.infrastructure.repository.employee.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/27
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserLeaveDetailQuery implements Serializable {
    private static final long serialVersionUID = -2227461077108558451L;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户账号
     */
    private String userCode;

    /**
     * 假期配置主键
     */
    private Long configId;
    /**
     * 假期配置主键集合
     */
    private List<Long> configIdList;

    /**
     * 假期名称
     */
    private String leaveName;
    /**
     * 假期名称集合
     */
    private List<String> leaveNameList;

    /**
     * 假期类型
     */
    private String leaveType;
    /**
     * 假期类型
     */
    private List<String> leaveTypeList;

    /**
     * 用户id集合
     */
    private List<Long> userIds;
    /**
     * 状态
     */
    private String status;
}
