package com.imile.attendance.infrastructure.repository.employee.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} UserLeaveRecordConditionQuery
 * {@code @since:} 2024-05-23 17:59
 * {@code @description:}用户请假记录查询条件
 */
@Data
public class UserLeaveRecordConditionQuery implements Serializable {

    private static final long serialVersionUID = 3543267559362377904L;

    /**
     * 用户id集合
     */
    private List<Long> userIdList;

    /**
     * dayId
     */
    private Long dayId;

    /**
     * dayId集合
     */
    private List<Long> dayIdList;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 类型(请假、销假)
     */
    private String type;

    /**
     * 操作用户code
     */
    private String operationUserCode;

    /**
     * 操作用户名称
     */
    private String operationUserName;
}
