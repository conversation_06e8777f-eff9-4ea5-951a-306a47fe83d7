package com.imile.attendance.infrastructure.repository.log.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.log.dao.LogOperationRecordDao;
import com.imile.attendance.infrastructure.repository.log.mapper.LogOperationRecordMapper;
import com.imile.attendance.infrastructure.repository.log.model.LogOperationRecordDO;
import com.imile.attendance.infrastructure.repository.log.query.LogRecordPageQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */
@Component
@RequiredArgsConstructor
public class LogOperationRecordDaoImpl extends ServiceImpl<LogOperationRecordMapper, LogOperationRecordDO> implements LogOperationRecordDao {

    @Resource
    private LogOperationRecordMapper logOperationRecordMapper;

    @Override
    public List<LogOperationRecordDO> listPage(LogRecordPageQuery logRecordPageQuery) {
        LambdaQueryWrapper<LogOperationRecordDO> queryWrapper = new LambdaQueryWrapper<LogOperationRecordDO>()
                .eq(LogOperationRecordDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(StringUtils.isNotBlank(logRecordPageQuery.getOperationModule()),
                        LogOperationRecordDO::getOperationModule, logRecordPageQuery.getOperationModule())
                .eq(StringUtils.isNotBlank(logRecordPageQuery.getOperationCode()),
                        LogOperationRecordDO::getOperationCode, logRecordPageQuery.getOperationCode())
                .and(StringUtils.isNotBlank(logRecordPageQuery.getOperator()),
                        wrapper -> wrapper.like(LogOperationRecordDO::getOperationUserCode, logRecordPageQuery.getOperator())
                                .or()
                                .like(LogOperationRecordDO::getOperationUserName, logRecordPageQuery.getOperator()))
                .like(StringUtils.isNotBlank(logRecordPageQuery.getOperationContent()),
                        LogOperationRecordDO::getRemark, logRecordPageQuery.getOperationContent());

        if (logRecordPageQuery.getOperationTimeStart() != null && logRecordPageQuery.getOperationTimeEnd() != null){
            queryWrapper.between(LogOperationRecordDO::getOperationTime,
                    logRecordPageQuery.getOperationTimeStart(), logRecordPageQuery.getOperationTimeEnd());
        }
        queryWrapper.orderByDesc(LogOperationRecordDO::getLastUpdDate);
        return list(queryWrapper);
    }
}

