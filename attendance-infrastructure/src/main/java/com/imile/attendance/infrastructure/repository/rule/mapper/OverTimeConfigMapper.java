package com.imile.attendance.infrastructure.repository.rule.mapper;

import java.util.List;

import com.imile.attendance.infrastructure.repository.rule.query.OverTimeConfigPageQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
@Mapper
@Repository
public interface OverTimeConfigMapper extends BaseMapper<OverTimeConfigDO> {

    /**
     * 根据国家列表查询国家级别的加班配置
     * 
     * @param countryList 国家列表
     * @return 国家级别的加班配置列表
     */
    List<OverTimeConfigDO> listCountryLevelConfigsByCountries(@Param("countryList") List<String> countryList);


    /**
     * 分页查询
     */
    List<OverTimeConfigDO> pageQuery(OverTimeConfigPageQuery query);
}
