package com.imile.attendance.rule;

import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.RuleConfigModifyDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
public interface PunchClassConfigManage {

    /**
     * 查询班次信息
     * 不区分是否最新
     *
     * @param id 班次ID
     * @return 班次规则信息
     */
    PunchClassConfigDTO selectById(Long id);

    /**
     * 查询最新启用班次信息
     *
     * @param id 班次ID
     * @return 班次规则信息
     */
    PunchClassConfigDTO selectLatestAndActiveById(Long id);

    /**
     * 批量查询班次信息
     * 不区分是否最新
     *
     * @param ids 班次ID列表
     * @return 班次规则信息
     */
    Map<Long, PunchClassConfigDTO> selectByIds(List<Long> ids);

    /**
     * 根据国家和班次性质查询最新的班次规则
     *
     * @param country     国家
     * @param classNature 班次性质
     * @return 最新的班次规则信息
     */
    List<PunchClassConfigDTO> selectByCountryAndClassNature(String country, String classNature);

    /**
     * 查询多班次用户最新班次列表
     *
     * @param userIdList 用户列表
     * @return 用户多班次信息Map
     */
    Map<Long, List<PunchClassConfigDO>> selectUserMultipleClassConfigList(List<Long> userIdList);


    /**
     * 根据用户集合查询当前最高优先级班次匹配规则
     * 仅针对固定班次
     *
     * @param userIdList 用户ID集合
     * @return 用户绑定的最高优先级班次规则Map Key: 用户ID Value: 班次ID
     */
    Map<Long, PunchClassConfigDO> selectTopPriorityByUserIds(Collection<Long> userIdList);


    /**
     * 根据用户集合查询当前停用的班次集合
     *
     * @return 班次ID
     */
    Set<Long> selectDisabledByUserIds(Collection<Long> userIdList);


    /**
     * 查询用户所有的最新且启用的班次列表
     *
     * @param userId      用户ID
     * @param classNature 班次性质
     * @return 最新班次列表
     */
    List<PunchClassConfigDTO> selectUserClassConfigList(Long userId, String classNature);


    /**
     * 根据部门或国家查询最高优先级班次
     *
     * @param deptId  部门ID
     * @param country 国家三字码
     * @return 最高优先级班次
     */
    PunchClassConfigDTO selectTopPriorityByDeptIdOrCountry(Long deptId, String country);

    /**
     * 查询最新的班次ID列表
     * 班次状态不过滤
     *
     * @param userIdList  用户ID集合
     * @param classNature 班次性质
     * @return 班次ID集合
     */
    Set<Long> selectLatestClassIdListByUserIds(List<Long> userIdList, String classNature);

    /**
     * 根据国家或部门查询所有启用的班次
     *
     * @param country     国家
     * @param deptId      部门
     * @param classNature 班次性质
     * @return
     */
    List<PunchClassConfigDO> selectLatestAndActiveByCountryOrDeptId(String country, Long deptId, String classNature);

    /**
     * 班次规则新增
     *
     * @param addPunchClassConfigDO             新增班次规则
     * @param addPunchClassItemConfigList       新增班次时段配置
     * @param addPunchClassConfigRangeList      新增班次适用范围
     * @param updatePunchClassConfigRangeDOList 更新班次适用范围
     */
    void punchClassConfigAdd(PunchClassConfigDO addPunchClassConfigDO,
                             List<PunchClassItemConfigDO> addPunchClassItemConfigList,
                             List<PunchClassConfigRangeDO> addPunchClassConfigRangeList,
                             List<PunchClassConfigRangeDO> updatePunchClassConfigRangeDOList);

    /**
     * 班次规则编辑
     */
    void punchClassConfigUpdate(List<PunchClassConfigDO> updatePunchClassConfigDOList,
                                List<PunchClassItemConfigDO> updatePunchClassItemConfigList,
                                List<PunchClassConfigRangeDO> updatePunchClassConfigRangeList);

    /**
     * 班次适用范围编辑
     */
    void punchClassConfigRangeUpdate(List<PunchClassConfigRangeDO> updatePunchClassConfigRangeList,
                                     List<PunchClassConfigRangeDO> addPunchClassConfigRangeList);

    /**
     * 状态启用
     *
     * @param classId 班次ID
     */
    void enableStatus(Long classId,
                      List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList);

    /**
     * 状态停用
     *
     * @param classId 班次ID
     */
    void disabledStatus(Long classId, Date currentTime);

    /**
     * 根据用户id和班次id删除适用范围
     *
     * @param userId   用户ID
     * @param classIds 班次ID集合
     */
    void deleteRangeByBizIdAndClassId(Long userId, List<Long> classIds);

    /**
     * 查询用户所有班次
     */
    List<RuleConfigModifyDTO> selectAllByBizId(Long bizId);

    /**
     * 班次适用范围批量操作
     */
    void classRangeBatchProcess(List<PunchClassConfigRangeDO> list, Consumer<List<PunchClassConfigRangeDO>> operation);

}
