package com.imile.attendance.vacation.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveDetailDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveStageDetailDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigCarryOverDao;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigCarryOverRangeDao;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigDao;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigIssueRuleDao;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigIssueRuleRangeDao;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigRangDao;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveItemConfigDao;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveJourneyConfigDao;
import com.imile.attendance.infrastructure.repository.vacation.dao.UserLeaveConfigHistoryDao;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigRangDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveItemConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveJourneyConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.UserLeaveConfigHistoryDO;
import com.imile.attendance.vacation.CompanyLeaveConfigManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 公司假期类型查询
 *
 * <AUTHOR>
 * @Date 2025/4/24
 */
@Service
@Slf4j
public class CompanyLeaveConfigManageImpl implements CompanyLeaveConfigManage {

    @Resource
    private CompanyLeaveConfigDao companyLeaveConfigDao;
    @Resource
    private CompanyLeaveItemConfigDao companyLeaveItemConfigDao;
    @Resource
    private UserLeaveDetailDao userLeaveDetailDao;
    @Resource
    private UserLeaveStageDetailDao userLeaveStageDetailDao;
    @Resource
    private UserLeaveRecordDao userLeaveRecordDao;
    @Resource
    private CompanyLeaveConfigRangDao companyLeaveConfigRangDao;
    @Resource
    private CompanyLeaveConfigIssueRuleDao companyLeaveConfigIssueRuleDao;
    @Resource
    private CompanyLeaveConfigIssueRuleRangeDao companyLeaveConfigIssueRuleRangeDao;
    @Resource
    private CompanyLeaveJourneyConfigDao companyLeaveJourneyConfigDao;
    @Resource
    private CompanyLeaveConfigCarryOverDao companyLeaveConfigCarryOverDao;
    @Resource
    private CompanyLeaveConfigCarryOverRangeDao companyLeaveConfigCarryOverRangeDao;
    @Resource
    private UserLeaveConfigHistoryDao userLeaveConfigHistoryDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCompanyLeave(CompanyLeaveConfigDO companyLeaveConfigDO, List<CompanyLeaveItemConfigDO> companyLeaveItemConfigDOS
            , List<UserLeaveDetailDO> userLeaveDetailDOList, List<UserLeaveDetailDO> updateUserLeaveDetailDOList
            , List<UserLeaveStageDetailDO> userLeaveStageDetailDOList, List<UserLeaveStageDetailDO> updateUserLeaveStageDetailDOList
            , List<UserLeaveRecordDO> recordDOList) {
        if (companyLeaveConfigDO != null) {
            companyLeaveConfigDao.save(companyLeaveConfigDO);
        }
        if (CollectionUtils.isNotEmpty(companyLeaveItemConfigDOS)) {
            companyLeaveItemConfigDao.saveBatch(companyLeaveItemConfigDOS);
        }
        if (CollectionUtils.isNotEmpty(userLeaveDetailDOList)) {
            userLeaveDetailDao.saveBatch(userLeaveDetailDOList);
        }
        if (CollectionUtils.isNotEmpty(updateUserLeaveDetailDOList)) {
            userLeaveDetailDao.updateBatchById(updateUserLeaveDetailDOList);
        }
        if (CollectionUtils.isNotEmpty(userLeaveStageDetailDOList)) {
            userLeaveStageDetailDao.saveBatch(userLeaveStageDetailDOList);
        }
        if (CollectionUtils.isNotEmpty(updateUserLeaveStageDetailDOList)) {
            userLeaveStageDetailDao.updateBatchById(updateUserLeaveStageDetailDOList);
        }
        if (CollectionUtils.isNotEmpty(recordDOList)) {
            userLeaveRecordDao.saveBatch(recordDOList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addWelfareLeaveConfig(CompanyLeaveConfigDO leaveConfig,
                                      List<CompanyLeaveConfigRangDO> leaveConfigRangList,
                                      CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                      List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeList,
                                      List<CompanyLeaveItemConfigDO> leaveItemConfigList,
                                      CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                      List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeList,
                                      List<CompanyLeaveJourneyConfigDO> leaveJourneyConfigList) {
        // 国家福利假期：主表
        if (ObjectUtil.isNotNull(leaveConfig)) {
            companyLeaveConfigDao.save(leaveConfig);
        }
        // 国家福利假期：范围表
        if (CollUtil.isNotEmpty(leaveConfigRangList)) {
            companyLeaveConfigRangDao.saveBatch(leaveConfigRangList);
        }
        // 国家福利假期：发放规则表
        if (ObjectUtil.isNotNull(leaveConfigIssueRule)) {
            companyLeaveConfigIssueRuleDao.save(leaveConfigIssueRule);
        }
        // 国家福利假期：发放规则：范围表
        if (CollUtil.isNotEmpty(leaveConfigIssueRuleRangeList)) {
            companyLeaveConfigIssueRuleRangeDao.saveBatch(leaveConfigIssueRuleRangeList);
        }
        // 国家福利假期：路途假规则表
        if (CollUtil.isNotEmpty(leaveJourneyConfigList)) {
            companyLeaveJourneyConfigDao.saveBatch(leaveJourneyConfigList);
        }
        // 国家福利假期：阶段表
        if (CollUtil.isNotEmpty(leaveItemConfigList)) {
            companyLeaveItemConfigDao.saveBatch(leaveItemConfigList);
        }
        // 国家福利假期：结转表
        if (ObjectUtil.isNotNull(leaveConfigCarryOver)) {
            companyLeaveConfigCarryOverDao.save(leaveConfigCarryOver);
        }
        // 国家福利假期：结转失效范围表
        if (ObjectUtil.isNotNull(leaveConfigCarryOverRangeList)) {
            companyLeaveConfigCarryOverRangeDao.saveBatch(leaveConfigCarryOverRangeList);
        }
    }

    @Override
    public void updateWelfareLeaveConfig(CompanyLeaveConfigDO leaveConfig,
                                         List<CompanyLeaveConfigRangDO> leaveConfigDeleteRangList,
                                         List<CompanyLeaveConfigRangDO> leaveConfigRangList) {
        // 国家福利假期：主表
        if (ObjectUtil.isNotNull(leaveConfig)) {
            companyLeaveConfigDao.updateById(leaveConfig);
        }
        // 国家福利假期：范围表(删除)
        if (CollUtil.isNotEmpty(leaveConfigDeleteRangList)) {
            companyLeaveConfigRangDao.updateBatchById(leaveConfigDeleteRangList);
        }
        // 国家福利假期：范围表
        if (CollUtil.isNotEmpty(leaveConfigRangList)) {
            companyLeaveConfigRangDao.saveBatch(leaveConfigRangList);
        }
    }

    @Override
    public void updateStatus(CompanyLeaveConfigDO leaveConfig, List<UserLeaveDetailDO> userLeaveDetailList) {
        if (ObjectUtil.isNotNull(leaveConfig)) {
            companyLeaveConfigDao.updateById(leaveConfig);
        }
        if (CollUtil.isNotEmpty(userLeaveDetailList)) {
            userLeaveDetailDao.updateBatchById(userLeaveDetailList);
        }
    }

    @Override
    public boolean saveOrUpdate(CompanyLeaveConfigDO configDO) {
        return companyLeaveConfigDao.saveOrUpdate(configDO);
    }

    @Override
    public boolean batchSaveItem(List<CompanyLeaveItemConfigDO> itemConfigDOList) {
        return companyLeaveItemConfigDao.saveBatch(itemConfigDOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlerUserLeaveConfigRange(List<CompanyLeaveConfigRangDO> addLeaveRang,
                                            List<CompanyLeaveConfigRangDO> updateLeaveRang) {
        if (CollectionUtils.isNotEmpty(addLeaveRang)) {
            companyLeaveConfigRangDao.saveBatch(addLeaveRang);
        }
        if (CollectionUtils.isNotEmpty(updateLeaveRang)) {
            companyLeaveConfigRangDao.updateBatchById(updateLeaveRang);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlerUserLeaveConfigHistoryRange(List<UserLeaveConfigHistoryDO> addUserLeaveHistoryList,
                                                   List<UserLeaveConfigHistoryDO> updateUserLeaveHistoryList) {
        if (CollectionUtils.isNotEmpty(addUserLeaveHistoryList)) {
            userLeaveConfigHistoryDao.saveBatch(addUserLeaveHistoryList);
        }
        if (CollectionUtils.isNotEmpty(updateUserLeaveHistoryList)) {
            userLeaveConfigHistoryDao.updateBatchById(updateUserLeaveHistoryList);
        }
    }
}
