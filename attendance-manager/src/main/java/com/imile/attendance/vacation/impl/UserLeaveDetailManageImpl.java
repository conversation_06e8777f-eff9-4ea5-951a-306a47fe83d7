package com.imile.attendance.vacation.impl;

import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveDetailDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveStageDetailDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.vacation.UserLeaveDetailManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022-05-23
 */
@Service
@Slf4j
public class UserLeaveDetailManageImpl implements UserLeaveDetailManage {

    @Autowired
    private UserLeaveDetailDao hrmsUserLeaveDetailDao;
    @Autowired
    private UserLeaveStageDetailDao userLeaveStageDetailDao;
    @Autowired
    private UserLeaveRecordDao hrmsUserLeaveRecordDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userLeaveMinutesInitUpdate(List<UserLeaveStageDetailDO> updateDetailList,
                                           List<UserLeaveRecordDO> updateRecordList) {
        if (CollectionUtils.isNotEmpty(updateDetailList)) {
            userLeaveStageDetailDao.updateBatchById(updateDetailList);
        }
        if (CollectionUtils.isNotEmpty(updateRecordList)) {
            hrmsUserLeaveRecordDao.updateBatchById(updateRecordList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userLeaveBalanceDaysUpdate(List<UserLeaveDetailDO> addUserLeaveDetailList,
                                           List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                           List<UserLeaveRecordDO> addUserLeaveRecordList,
                                           List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                           List<UserLeaveDetailDO> updateUserLeaveDetailDOList) {
        if (CollectionUtils.isNotEmpty(addUserLeaveDetailList)) {
            hrmsUserLeaveDetailDao.saveBatch(addUserLeaveDetailList);
        }
        if (CollectionUtils.isNotEmpty(addUserLeaveStageDetailList)) {
            userLeaveStageDetailDao.saveBatch(addUserLeaveStageDetailList);
        }
        if (CollectionUtils.isNotEmpty(addUserLeaveRecordList)) {
            hrmsUserLeaveRecordDao.saveBatch(addUserLeaveRecordList);
        }
        if (CollectionUtils.isNotEmpty(updateUserLeaveStageDetailList)) {
            userLeaveStageDetailDao.updateBatchById(updateUserLeaveStageDetailList);
        }
        if (CollectionUtils.isNotEmpty(updateUserLeaveDetailDOList)) {
            hrmsUserLeaveDetailDao.updateBatchById(updateUserLeaveDetailDOList);
        }
    }
}
