package com.imile.attendance.context;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 用户上下文
 */
@Data
@Accessors(chain = true)
public class UserContext {

    /**
     * 用户ID
     */
    private Long id;

    private String clientCode;

    private String deviceId;

    private String phone;

    private String password;

    private boolean accountNonExpired;

    private boolean accountNonLocked;

    private boolean credentialsNonExpired;

    private boolean enabled;

    private String userCode;
    /**
     * 供应商orgid
     */
    private Long orgId;

    private Long ocId;

    private String ocCode;

    private String userType;

    private String userName;

    private String userNameEn;

    private String timeZone;

    private String language;

    private Date loginTime;

    /**
     * 管理员属性 系统管理员（SYSTEM）、子公司管理员（SUBSIDIARY）、非管理员(NONE)
     */
    private String administratorAttributes;

    /**
     * 组织
     */
    private List<Long> organizationIds = new ArrayList<>();

    /**
     * 国家
     */
    private List<String> countryList = new ArrayList<>();



    /**
     * 用户数据权限
     */
    private Map<String, List<String>> permissionMap = new HashMap<>();

    /**
     * 角色列表
     */
    private List<Long> roleList = new ArrayList<>();

    /**
     * 是否超级管理员
     */
    private boolean isSystem = false;



}
