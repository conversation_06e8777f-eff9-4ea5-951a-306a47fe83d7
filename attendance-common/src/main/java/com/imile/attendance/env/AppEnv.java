package com.imile.attendance.env;

import lombok.Getter;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
@Component
public class AppEnv {

    /**
     * 本地环境
     */
    public static final String LOCAL = "local";
    /**
     * 开发环境
     */
    public static final String DEV = "DEV";
    /**
     * 测试环境
     */
    public static final String TEST = "TEST";
    /**
     * uat环境
     */
    public static final String HK_UAT = "UAT";
    /**
     * de-uat
     */
    public static final String DE_UAT = "UAT";
    /**
     * 生产环境
     */
    public static final String PRODUCTION = "prod";

    @Getter
    private static boolean debugSafe;

    private static String profile;

    private static final Set<String> DEBUG_SAFE_ENV = new HashSet<>(Arrays.asList(LOCAL, DEV, TEST));

    public static boolean isLocal() {
        return profile.equals(LOCAL);
    }

    public static boolean isDev() {
        return profile.equals(DEV);
    }

    public static boolean isTest() {
        return profile.equals(TEST);
    }

    public static boolean isHkUat() {
        return profile.equals(HK_UAT);
    }

    public static boolean isDeUat(){
        return profile.equals(DE_UAT);
    }

    public static boolean isProduction() {
        return profile.equals(PRODUCTION);
    }

    /**
     * 获取 profile 关联前缀，用于非生产环境数据用同一个数据源时隔离
     *
     * @param appender 后缀
     * @return 前缀
     */
    public static String getPrefix(String appender) {
        return isProduction() ? "" : profile + appender;
    }

    public AppEnv(Environment env) {
        String[] profiles = env.getActiveProfiles();
        profile = profiles.length == 0 ? LOCAL : profiles[0];
        debugSafe = DEBUG_SAFE_ENV.contains(profile);
    }

    /**
     * 用于可能 AppEnv 还未初始化的场景
     */
    public boolean debugSafe() {
        return debugSafe;
    }

}
