package com.imile.attendance.enums;


import lombok.Getter;

import java.util.Objects;

/**
 * 班次类型
 *
 * <AUTHOR>
 * @since 2025/4/7
 */
@Getter
public enum ClassTypeEnum {

    DAY_SHIFT(1, "白班", "day shift"),
    NIGHT_SHIFT(2, "晚班", "night shift"),

    ;

    private final Integer code;

    private final String desc;

    private final String descEn;


    ClassTypeEnum(Integer code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static String getDesc(Integer code) {
        for (ClassTypeEnum value : ClassTypeEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
