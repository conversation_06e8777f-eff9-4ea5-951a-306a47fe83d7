package com.imile.attendance.enums;


import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Getter
public enum SystemRoleEnum {
    AREA_SYS(202112010009L, "区域管理员", "AREA_SYS", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    ATTENDANCE_SYS(202112010007L, "考勤管理员", "ATTENDANCE_SYS", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    CHN_HR_TA(202112010029L, "CHN HR (TA)", "CHN_HR_TA", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    CHN_HRM(202112010030L, "CHN HRM", "CHN_HRM", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    COUNTRY_HR_C_B(202112010020L, "Country HR（薪资）", "COUNTRY_HR_C_B", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    COUNTRY_HR_P(202112010022L, "Country HR（绩效）", "COUNTRY_HR_P", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    COUNTRY_HR_P_S(202112010019L, "Country HR（人事+系统）", "COUNTRY_HR_P_S", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    COUNTRY_HR_T(202112010021L, "Country HR（培训）", "COUNTRY_HR_T", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    COUNTRY_HR_TA(202112010023L, "Country HR（招聘）", "COUNTRY_HR_TA", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    COUNTRY_HR_TA_P(202112010031L, "Country HR（招聘+人事）", "COUNTRY_HR_TA_P", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    DEPT_SYS(202112010004L, "运营部门管理员", "DEPT_SYS", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    DRIVER_SYS(202112010010L, "司机管理员", "DRIVER_SYS", ResourceTypeEnum.COMPANY_ID, RoleTypeEnum.ORG),
    FIN_COST_DATA(202112010027L, "FIN-Cost data", "FIN_COST_DATA", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HO_DEPT_SYS(202112010011L, "办公室部门管理员", "HO_DEPT_SYS", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HOD(202112010025L, "国家二级部门负责人", "HOD", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HQ_HR_C_B(202112010014L, "HQ HR（薪资）", "HQ_HR_C_B", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HQ_HR_O_P(202112010016L, "HQ HR（组织+绩效）", "HQ_HR_O_P", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HQ_HR_P_S(202112010013L, "HQ HR（人事+系统）", "HQ_HR_P_S", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HQ_HR_T(202112010015L, "HQ HR（培训）", "HQ_HR_T", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HQ_HR_TA(202112010017L, "HQ HR（招聘）", "HQ_HR_TA", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HRD(202112010018L, "HRD", "HRD", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HRM(202112010024L, "HRM", "HRM", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HUB_LEADER(202112010026L, "Hub Leader", "HUB_LEADER", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    NONE(202112010003L, "非管理员", "NONE", ResourceTypeEnum.NONE, RoleTypeEnum.NONE),
    SUBSIDIARY(202112010002L, "子公司管理员", "SUBSIDIARY", ResourceTypeEnum.COMPANY_ID, RoleTypeEnum.ORG),
    SYSTEM(202112010001L, "系统管理员", "SYSTEM", ResourceTypeEnum.ALL, RoleTypeEnum.SYSTEM),
    SYSTEM_ITEM(202112010012L, "非薪资管理员", "SYSTEM_ITEM", ResourceTypeEnum.ALL, RoleTypeEnum.SYSTEM),
    SYSTEM_STAFF(202112010006L, "系统员工管理员", "SYSTEM_STAFF", ResourceTypeEnum.ALL, RoleTypeEnum.SYSTEM),
    TA_ADMIN(202112010028L, "招聘管理员", "TA_ADMIN", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    TRAINING_SYS(202112010008L, "培训管理员", "TRAINING_SYS", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    VENDOR_SYS(202112010005L, "供应商管理员", "VENDOR_SYS", ResourceTypeEnum.VENDOR_ID, RoleTypeEnum.VENDOR),
    ;


    private Long roleId;

    private String roleNameCn;

    private String code;

    private ResourceTypeEnum resourceType;

    private RoleTypeEnum roleType;


    SystemRoleEnum(Long roleId, String roleNameCn, String code, ResourceTypeEnum resourceType, RoleTypeEnum roleType) {
        this.roleId = roleId;
        this.roleNameCn = roleNameCn;
        this.code = code;
        this.resourceType = resourceType;
        this.roleType = roleType;
    }

    private static final Map<Long, SystemRoleEnum> cacheMap = new ConcurrentHashMap<>();


    static {
        SystemRoleEnum[] attributes = values();
        int var1 = attributes.length;

        for (int index = 0; index < var1; ++index) {
            SystemRoleEnum codeEnum = attributes[index];
            cacheMap.put(codeEnum.getRoleId(), codeEnum);
        }
    }


    public static String getPriorityRoleCode(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return null;
        }
        roleIdList.sort(Comparator.comparingLong(SystemRoleEnum::getRolePriority).reversed());
        // 获取排序后的角色列表中的第一个角色
        Long highestPriorityRoleId = roleIdList.get(0);
        // 根据角色ID获取角色代码
        return getRoleCode(highestPriorityRoleId);

    }

    public static String getRoleCode(Long roleId) {
        if (roleId == null) {
            return null;
        }
        SystemRoleEnum systemRoleEnum = cacheMap.get(roleId);
        if (systemRoleEnum == null) {
            return null;
        }
        return systemRoleEnum.getCode();
    }

    // 获取角色优先级，自定义优先级顺序
    private static long getRolePriority(Long roleId) {
        SystemRoleEnum systemRoleEnum = cacheMap.get(roleId);
        return systemRoleEnum != null ? systemRoleEnum.getPriority() : Long.MIN_VALUE;
    }


    // 添加一个用于比较优先级的方法
    private long getPriority() {
        // 自定义角色优先级顺序，你可以根据实际需求进行调整
        switch (this) {
            case SYSTEM:
            case SYSTEM_ITEM:
            case SYSTEM_STAFF:
                return 3;
            case VENDOR_SYS:
            case TRAINING_SYS:
            case AREA_SYS:
            case ATTENDANCE_SYS:
            case CHN_HR_TA:
            case CHN_HRM:
            case COUNTRY_HR_C_B:
            case COUNTRY_HR_P:
            case COUNTRY_HR_P_S:
            case COUNTRY_HR_T:
            case COUNTRY_HR_TA:
            case COUNTRY_HR_TA_P:
            case DEPT_SYS:
            case DRIVER_SYS:
            case FIN_COST_DATA:
            case HO_DEPT_SYS:
            case HOD:
            case HQ_HR_C_B:
            case HQ_HR_O_P:
            case HQ_HR_P_S:
            case HQ_HR_T:
            case HQ_HR_TA:
            case HRD:
            case HRM:
            case HUB_LEADER:
            case SUBSIDIARY:
            case TA_ADMIN:
                return 2;
            case NONE:
                return 1;
            default:
                return 0; // 默认优先级
        }
    }


    public static boolean isSystem(List<Long> roleList) {
        if (CollectionUtils.isEmpty(roleList)) {
            return false;
        }
        for (Long roleId : roleList) {
            SystemRoleEnum systemRoleEnum = cacheMap.get(roleId);
            if (systemRoleEnum == null) {
                continue;
            }
            if (Objects.equals(systemRoleEnum.getRoleType(), RoleTypeEnum.SYSTEM)) {
                return true;
            }
        }
        return false;
    }


}
