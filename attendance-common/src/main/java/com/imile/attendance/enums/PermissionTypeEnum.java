package com.imile.attendance.enums;


import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum PermissionTypeEnum {
    DEPT("BaseDeptData1", "部门数据权限"),
    COUNTRY("getCountryList1", "常驻国数据权限"),
    VENDOR("BaseSupplierData", "供应商数据权限"),
    ;

    private String typeCode;
    private String typeName;

    PermissionTypeEnum(String typeCode, String typeName) {
        this.typeCode = typeCode;
        this.typeName = typeName;
    }
}
