package com.imile.attendance.enums.vacation;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * {@code @author:} allen
 * {@code @className:} LeaveConfigIsCarryOverEnum
 * {@code @since:} 2024-04-15 16:50
 * {@code @description:}
 */
@Getter
public enum LeaveConfigIsCarryOverEnum {
    DEFAULT(0, "", ""),
    YES(1, "可结转", "Carry over"),
    NO(2, "不可结转", "Not carry over"),
    ;

    private final Integer type;

    private final String desc;

    private final String descEn;

    private static final Map<Integer, LeaveConfigIsCarryOverEnum> CACHE_MAP = new ConcurrentHashMap<>();

    static {
        for (LeaveConfigIsCarryOverEnum codeEnum : values()) {
            CACHE_MAP.put(codeEnum.getType(), codeEnum);
        }
    }

    LeaveConfigIsCarryOverEnum(Integer type, String desc, String descEn) {
        this.type = type;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static LeaveConfigIsCarryOverEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return CACHE_MAP.get(type);
    }
}
