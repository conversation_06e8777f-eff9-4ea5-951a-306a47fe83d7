package com.imile.attendance.enums;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceTypeEnum
 * {@code @since:} 2024-01-22 14:57
 * {@code @description:}
 */
@Getter
public enum DriverAttendanceTypeEnum {
    DEFAULT(0, "",""),
    PRESENT(1, "出勤","P"),
    ABSENT(2, "缺勤","A"),
    LEAVE(3, "请假","L"),
    ;

    private final Integer type;

    private final String desc;

    private final String descEn;

    private static final Map<Integer, DriverAttendanceTypeEnum> CACHE_MAP = new ConcurrentHashMap<>();

    static {
        for (DriverAttendanceTypeEnum codeEnum : values()) {
            CACHE_MAP.put(codeEnum.getType(), codeEnum);
        }
    }

    DriverAttendanceTypeEnum(Integer type, String desc, String descEn) {
        this.type = type;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static DriverAttendanceTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return CACHE_MAP.get(type);
    }
}
