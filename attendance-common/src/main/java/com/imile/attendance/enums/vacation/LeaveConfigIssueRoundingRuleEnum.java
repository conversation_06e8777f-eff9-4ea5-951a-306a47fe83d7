package com.imile.attendance.enums.vacation;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * {@code @author:} allen
 * {@code @className:} LeaveConfigIssueRoundingRule
 * {@code @since:} 2024-04-15 16:36
 * {@code @description:}
 */
@Getter
public enum LeaveConfigIssueRoundingRuleEnum {
    DEFAULT(0, "", ""),
    ROUND(1, "四舍五入", "round"),
    ROUND_DOWN(2, "向下取整", "Round down"),
    ROUND_UP(3, "向上取整", "Round up"),
    ;

    private final Integer type;

    private final String desc;

    private final String descEn;

    private static final Map<Integer, LeaveConfigIssueRoundingRuleEnum> CACHE_MAP = new ConcurrentHashMap<>();

    static {
        for (LeaveConfigIssueRoundingRuleEnum codeEnum : values()) {
            CACHE_MAP.put(codeEnum.getType(), codeEnum);
        }
    }

    LeaveConfigIssueRoundingRuleEnum(Integer type, String desc, String descEn) {
        this.type = type;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static LeaveConfigIssueRoundingRuleEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return CACHE_MAP.get(type);
    }
}
