package com.imile.attendance.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

public class ConverterDateUtil {


    private static final ZoneId SYS_ZONE_ID = ZoneId.of("GMT+8");

    /**
     * date向localDateTime转化
     */
    public static LocalDateTime toLocalDateTime(Date time) {
        return time == null ? null : LocalDateTime.ofInstant(time.toInstant(), SYS_ZONE_ID);
    }

    /**
     * localDateTime向date转化
     *
     */
    public static Date toDate(LocalDateTime time) {
        return time == null ? null : Date.from(time.atZone(SYS_ZONE_ID).toInstant());
    }

    public static LocalDate toLocalDate(Date time) {
        return time == null ? null : time.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
    }

    public static Date toDate(LocalDate time) {
        return time == null ? null : Date.from(time.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }
}
