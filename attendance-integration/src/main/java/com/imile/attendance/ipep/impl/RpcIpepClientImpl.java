package com.imile.attendance.ipep.impl;

import com.imile.attendance.ipep.RpcIpepClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.ipep.api.IpepAuthService;
import com.imile.ipep.api.OssApi;
import com.imile.ipep.dto.NoLoginTokenInfoDTO;
import com.imile.ipep.dto.OssApiDto;
import com.imile.ipep.dto.OssStsApiDTO;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/2 
 * @Description
 */
@Component
public class RpcIpepClientImpl implements RpcIpepClient {


    @Reference(version = "1.0.0", check = false, timeout = 10000, retries = 0)
    private OssApi ossApi;

    @Reference(version = "1.0.0", check = false, timeout = 10000, retries = 0)
    private IpepAuthService ipepAuthService;

    @Override
    public NoLoginTokenInfoDTO getNoLoginTokenInfo(String appId) {
        return RpcResultProcessor.process(ipepAuthService.getNoLoginTokenInfo(appId));
    }

    @Override
    public OssApiDto getUrlByFileKey(String fileKey, Integer bucketType) {
        return RpcResultProcessor.process(ossApi.getUrlByFileKey(fileKey, bucketType));
    }

    @Override
    public OssApiDto getUrlByFileKey(String fileKey, Integer bucketType, String urlType) {
        return RpcResultProcessor.process(ossApi.getUrlByFileKey(fileKey, bucketType, urlType));
    }

    @Override
    public List<OssApiDto> getUrlByFileKeys(List<String> fileKeys, Integer bucketType) {
        return RpcResultProcessor.process(ossApi.getUrlByFileKeys(fileKeys, bucketType));
    }

    @Override
    public OssStsApiDTO getStsSignature(String userCode, String sysName) {
        return RpcResultProcessor.process(ossApi.getStsSignature(userCode, sysName));
    }

    @Override
    public OssStsApiDTO getStsSignature(String userCode, String sysName, Integer bucketType) {
        return RpcResultProcessor.process(ossApi.getStsSignature(userCode, sysName, bucketType));
    }

    @Override
    public String getBucketURL(Integer bucketType) {
        return RpcResultProcessor.process(ossApi.getBucketURL(bucketType));
    }

    @Override
    public Boolean isExistsOfFile(String fileKey, Integer bucketType) {
        return RpcResultProcessor.process(ossApi.isExistsOfFile(fileKey, bucketType));
    }
}
