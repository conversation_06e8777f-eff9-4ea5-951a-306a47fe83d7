package com.imile.attendance.bpm;

import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiQuery;
import com.imile.bpm.mq.dto.ApprovalInfoCreateResultDTO;
import com.imile.bpm.mq.dto.ApprovalInitInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalUpdateContentApiDTO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/9 
 * @Description
 */
public interface RpcBpmApprovalClient {

    /**
     * 发起审批
     */
    ApprovalInfoCreateResultDTO addApprovalInfo(ApprovalInitInfoApiDTO initInfoApiDTO);

    /**
     * 审批模板预览
     */
    List<ApprovalEmptyRecordApiDTO> getEmptyApprovalRecords(ApprovalEmptyRecordApiQuery query);

    /**
     * HR撤销操作
     */
    void backApply(Long approvalId);

    /**
     * 查询审批的所有审批人
     */
    List<String> getApprovalUserCodes(Long approvalId);

    void updateApprovalContentV2(ApprovalUpdateContentApiDTO updateContentApiDTO);
}
