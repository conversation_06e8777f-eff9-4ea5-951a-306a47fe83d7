package com.imile.attendance.shift.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.excel.ExcelImport;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/4/21 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserShiftImportDTO extends ExcelImport {


    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 排班日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String classDate;
    /**
     * 班次名称
     */
    private String className;



    //=====================后面字段都是业务逻辑字段========================

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 当天的排班规则，可以为班次名称，也可以是OFF H
     */
    private String dayShiftRule;

    /**
     * 排班日期
     */
    private Date date;

    /**
     * 排班天
     */
    private Long classDayId;

    /**
     * 班次ID 当为H,OFF时，为空
     */
    private Long classId;

    private Long year;

    /**
     * 01 02这种形式
     */
    private String month;

    private Long day;

    private String yearMonth;

    /**
     * 考勤日历ID
     */
    private Long attendanceConfigId;

}
