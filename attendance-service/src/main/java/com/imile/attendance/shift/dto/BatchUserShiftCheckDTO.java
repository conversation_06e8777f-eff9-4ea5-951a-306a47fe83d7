package com.imile.attendance.shift.dto;

import com.imile.attendance.context.RequestInfoHolder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18 
 * @Description
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchUserShiftCheckDTO {

    private List<Long> userIdList;

    private Integer currentUserCount;

    private List<NotMatchUserDTO> notMatchUserDTOList;



    public static BatchUserShiftCheckDTO initEmpty() {
       return BatchUserShiftCheckDTO.builder()
                .userIdList(Collections.emptyList())
                .currentUserCount(0)
                .notMatchUserDTOList(Collections.emptyList())
                .build();
    }

    public boolean isAllMatch() {
        return CollectionUtils.isEmpty(notMatchUserDTOList);
    }

    public boolean isAnyNotMatch() {
        return CollectionUtils.isNotEmpty(notMatchUserDTOList);
    }

    public List<String> getNotMatchUserNameList() {
        if (CollectionUtils.isEmpty(notMatchUserDTOList)) {
            return Collections.emptyList();
        }
        return notMatchUserDTOList.stream()
                .map(NotMatchUserDTO::getLocaleUserName)
                .collect(Collectors.toList());
    }

    public String getNotMatchUserNameStr() {
        return String.join(",", getNotMatchUserNameList());
    }


    @Data
    public static class NotMatchUserDTO {
        private Long userId;
        private String userCode;
        private String userName;
        private String userNameEn;

        public String getLocaleUserName() {
            if (StringUtils.isEmpty(userName)) {
                return null;
            }
            return RequestInfoHolder.isChinese() ? userName : userCode;
        }
    }
}
