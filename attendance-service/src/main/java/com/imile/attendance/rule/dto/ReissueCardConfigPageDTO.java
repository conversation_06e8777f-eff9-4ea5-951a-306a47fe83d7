package com.imile.attendance.rule.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16 
 * @Description
 */
@Data
public class ReissueCardConfigPageDTO {

    private Long id;

    /**
     * 状态
     */
    private String status;

    /**
     * 国家
     */
    private String country;

    /**
     * 补卡规则编码
     */
    private String configNo;

    /**
     * 是否为国家级别规则
     */
    private Integer isCountryLevel;

    /**
     * 补卡规则名称
     */
    private String configName;

    /**
     * 每月最大补卡次数
     */
    private Integer maxRepunchNumber;

    /**
     * 适用范围
     */
    private List<ConfigRangeDTO> rangeRecords;

    /**
     * 已绑定员工数
     */
    private Integer employeeCount;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 最后修改人
     */
    private String lastUpdUserName;

    /**
     * 最近修改日期
     */
    private Date lastUpdDate;
}
