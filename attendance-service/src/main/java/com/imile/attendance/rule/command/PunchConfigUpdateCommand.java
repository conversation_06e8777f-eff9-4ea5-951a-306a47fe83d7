package com.imile.attendance.rule.command;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PunchConfigUpdateCommand extends PunchConfigAddCommand {

    /**
     * 打卡规则编码
     */
    @NotBlank(message = "punchConfigNo can not be null")
    private String punchConfigNo;
}
