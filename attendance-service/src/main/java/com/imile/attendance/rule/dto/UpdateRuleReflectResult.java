package com.imile.attendance.rule.dto;

import lombok.Builder;
import lombok.Data;

import java.util.Collections;

/**
 * 考勤规则更新影响结果
 * 用于描述规则更新后对配置和用户范围的影响
 *
 * <AUTHOR> chen
 * @since 2025/4/14
 */
@Data
@Builder
public class UpdateRuleReflectResult {

    /**
     * 是否需要更新配置内容
     * true: 规则内容发生变更，需要更新配置
     * false: 规则内容未变更
     */
    private Boolean isNeedUpdateConfig;

    /**
     * 是否需要更新适用范围
     * true: 规则适用范围发生变更，需要更新用户范围
     * false: 规则适用范围未变更
     */
    private Boolean isNeedUpdateRange;

    /**
     * 配置更新影响的用户数量(当前规则应用的用户总数)
     */
    private Integer updateConfigReflectUserSize;

    /**
     * 新增适用范围的用户数量
     * 当规则范围变更时，表示新增的适用用户数量
     */
    private Integer updateRangeReflectAddUserSize;

    /**
     * 移除适用范围的用户数量
     * 当规则范围变更时，表示被移除的用户数量
     */
    private Integer updateRangeReflectRemoveUserSize;

    private RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO;

    /**
     * 创建一个表示无影响的结果对象
     * 用于规则未发生任何变更的情况
     *
     * @return 所有字段都为空或0的结果对象
     */
    public static UpdateRuleReflectResult noReflect() {
        return UpdateRuleReflectResult.builder()
                .isNeedUpdateConfig(false)
                .isNeedUpdateRange(false)
                .updateConfigReflectUserSize(0)
                .updateRangeReflectAddUserSize(0)
                .updateRangeReflectRemoveUserSize(0)
                .ruleConfigChangeCheckDTO(RuleConfigChangeCheckDTO.buildSuccess())
                .build();
    }

    public static UpdateRuleReflectResult rangeCheckFail(RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO) {
        return UpdateRuleReflectResult.builder()
                .isNeedUpdateConfig(false)
                .isNeedUpdateRange(false)
                .updateConfigReflectUserSize(0)
                .updateRangeReflectAddUserSize(0)
                .updateRangeReflectRemoveUserSize(0)
                .ruleConfigChangeCheckDTO(ruleConfigChangeCheckDTO)
                .build();
    }
}