package com.imile.attendance.rule.event.domain;

import com.imile.attendance.rule.dto.PunchClassConfigAddAutoShiftDTO;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2025/4/14
 */
public class PunchClassConfigAddEvent extends ApplicationEvent {
    private final PunchClassConfigAddAutoShiftDTO classConfigAddAutoShiftDTO;

    public PunchClassConfigAddAutoShiftDTO getData() {
        return classConfigAddAutoShiftDTO;
    }

    public PunchClassConfigAddEvent(Object source, PunchClassConfigAddAutoShiftDTO classConfigAddAutoShiftDTO) {
        super(source);
        this.classConfigAddAutoShiftDTO = classConfigAddAutoShiftDTO;
    }
}
