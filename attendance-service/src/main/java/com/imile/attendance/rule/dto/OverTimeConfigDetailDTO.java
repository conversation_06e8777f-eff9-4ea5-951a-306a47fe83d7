package com.imile.attendance.rule.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17 
 * @Description
 */
@Data
public class OverTimeConfigDetailDTO {

    /**
     *   主键id
     */
    private Long id;

    /**
     * 规则名称
     */
    private String configName;

    /**
     * 规则编码
     */
    private String configNo;

    /**
     * 是否为国家级别规则
     */
    private Integer isCountryLevel;

    /**
     * 工作加班开始时间
     */
    private BigDecimal workingOutStartTime;

    /**
     * 工作日最长有效加班时间
     */
    private BigDecimal workingEffectiveTime;

    /**
     * 工作日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    private String workingSubsidyType;

    //==========休息日加班时长的判定====================

    /**
     * 休息日最长有效加班时间
     *
     */
    private BigDecimal restEffectiveTime;

    /**
     * 休息日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    private String restSubsidyType;

    //==========节假日加班时长的判定====================

    /**
     * 节假日最长有效加班时间
     *
     */
    private BigDecimal holidayEffectiveTime;

    /**
     * 节假日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    private String holidaySubsidyType;

    /**
     * 适用国家
     */
    private String country;

    /**
     * 适用部门
     */
    private List<ConfigRangeDTO> applyDeptList;

    /**
     * 适用用户
     */
    private List<ConfigRangeDTO> applyUserList;

    /**
     * 当部门/用户已存在其他打卡方案时，是否强制覆盖 1：强制覆盖  0：不覆盖
     */
    private Integer isCoverOld;
}
