package com.imile.attendance.rule.command;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
public class PunchClassItemConfigCommand implements Serializable {

    private Long id;

    /**
     * 班次规则ID
     */
    private Long punchClassId;

    /**
     * 序号
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private Integer sortNo;

    /**
     * 上班时间 仅有时分秒信息，形如：09:00:00
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private String punchInTime;

    /**
     * 下班时间 仅有时分秒信息，形如：18:00:00
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private String punchOutTime;

    /**
     * 最早上班打卡时间 仅有时分秒信息，形如： 09:00:00
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private String earliestPunchInTime;

    /**
     * 最晚上班打卡时间 仅有时分秒信息
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private String latestPunchInTime;

    /**
     * 最晚下班打卡时间 仅有时分秒信息，形如：19:00:00
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private String latestPunchOutTime;

    /**
     * 是否跨天 跨天是指打卡时间是第二天：0：不跨天，1：跨天
     */
    private Integer isAcross;

    /**
     * 弹性时间
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    @Max(value = 12, message = ValidCodeConstant.MAX)
    @Min(value = 0, message = ValidCodeConstant.MIN)
    private BigDecimal elasticTime;

    /**
     * 法定工作时长（不包含休息时间）
     */
    @Min(value = 0, message = ValidCodeConstant.MIN)
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private BigDecimal legalWorkingHours;

    /**
     * 出勤时长（包含休息时间）
     */
    @Min(value = 0, message = ValidCodeConstant.MIN)
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private BigDecimal attendanceHours;

    /**
     * 休息开始时间 仅有时分秒信息，形如：09:00:00
     */
    private String restStartTime;

    /**
     * 休息结束时间 仅有时分秒信息，形如：09:00:00
     */
    private String restEndTime;
}
