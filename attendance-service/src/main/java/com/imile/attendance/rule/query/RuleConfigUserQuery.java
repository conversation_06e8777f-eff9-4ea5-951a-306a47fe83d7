package com.imile.attendance.rule.query;

import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RuleConfigUserQuery extends ResourceQuery {

    /**
     * 规则编码
     */
    private String configNo;

    /**
     * 员工常驻地国家
     */
    private String locationCountry;

    /**
     * 员工姓名/工号
     */
    private String userCodeOrName;

    /**
     * 员工部门
     */
    private Long deptId;

    /**
     * 员工部门集合
     */
    private List<Long> deptIds;
}
