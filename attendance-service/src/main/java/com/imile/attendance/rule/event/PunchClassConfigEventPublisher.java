package com.imile.attendance.rule.event;

import com.imile.attendance.rule.dto.PunchClassConfigAddAutoShiftDTO;
import com.imile.attendance.rule.dto.PunchClassConfigSwitchStatusDTO;
import com.imile.attendance.rule.dto.PunchClassConfigUpdateAutoShiftDTO;
import com.imile.attendance.rule.event.domain.PunchClassConfigAddEvent;
import com.imile.attendance.rule.event.domain.PunchClassConfigSwitchStatusEvent;
import com.imile.attendance.rule.event.domain.PunchClassConfigUpdateEvent;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/4/14
 */
@Component
@RequiredArgsConstructor
public class PunchClassConfigEventPublisher {

    private final ApplicationEventPublisher publisher;

    public void sendPunchClassConfigAddEvent(PunchClassConfigAddAutoShiftDTO classConfigAddAutoShiftDTO) {
        publisher.publishEvent(new PunchClassConfigAddEvent(this, classConfigAddAutoShiftDTO));
    }

    public void sendPunchClassConfigUpdateEvent(PunchClassConfigUpdateAutoShiftDTO punchClassConfigUpdateAutoShiftDTO) {
        publisher.publishEvent(new PunchClassConfigUpdateEvent(this, punchClassConfigUpdateAutoShiftDTO));
    }

    public void sendPunchClassConfigSwitchStatusEvent(PunchClassConfigSwitchStatusDTO configSwitchStatusDTO) {
        publisher.publishEvent(new PunchClassConfigSwitchStatusEvent(this, configSwitchStatusDTO));
    }
}
