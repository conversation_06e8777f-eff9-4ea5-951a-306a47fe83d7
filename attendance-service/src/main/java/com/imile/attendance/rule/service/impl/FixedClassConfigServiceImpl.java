package com.imile.attendance.rule.service.impl;

import com.imile.attendance.annon.Strategy;
import com.imile.attendance.calendar.dto.CalendarAndPunchHandlerDTO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigRangeDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.rule.dto.PunchClassConfigAddAutoShiftDTO;
import com.imile.attendance.rule.dto.PunchClassConfigAddDTO;
import com.imile.attendance.rule.dto.PunchClassConfigRangeDifferDTO;
import com.imile.attendance.rule.dto.PunchClassConfigSwitchStatusDTO;
import com.imile.attendance.rule.service.PunchClassConfigAbstractService;
import com.imile.attendance.rule.service.PunchClassConfigService;
import com.imile.attendance.rule.vo.PunchClassConfigAddVO;
import com.imile.attendance.rule.vo.PunchClassConfigUpdateConfirmVO;
import com.imile.attendance.shift.param.UserAutoShiftParam;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import com.imile.util.user.UserEvnHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 固定班次实现
 *
 * <AUTHOR>
 * @since 2025/4/14
 */
@Slf4j
@Service
@Strategy(value = PunchClassConfigService.class, implKey = "FixedClassConfigServiceImpl")
public class FixedClassConfigServiceImpl extends PunchClassConfigAbstractService {


    @Override
    public boolean isMatch(ClassNatureEnum classNatureEnum) {
        return ClassNatureEnum.FIXED_CLASS.equals(classNatureEnum);
    }

    @Override
    public void userEntryAutoShift(UserInfoDO userInfo, AttendanceUserEntryRecord userEntryRecordDO) {
        //实际确认入职时间
        Date confirmDate = userEntryRecordDO.getConfirmDate();
        Long confirmDayId = DateHelper.getDayId(confirmDate);

        PunchClassConfigDTO punchClassConfigDTO = punchClassConfigManage.selectTopPriorityByDeptIdOrCountry(userInfo.getDeptId(), userInfo.getLocationCountry());
        if (Objects.isNull(punchClassConfigDTO)) {
            log.info("{}: 用户入职排班处理：当前无班次规则无需处理", userInfo.getUserCode());
            return;
        }

        String rangeType = Objects.equals(BusinessConstant.Y, punchClassConfigDTO.getIsCountryLevel()) ? RuleRangeTypeEnum.COUNTRY.getCode() : RuleRangeTypeEnum.DEPT.getCode();

        PunchClassConfigRangeDO classConfigRangeDO = new PunchClassConfigRangeDO();
        classConfigRangeDO.setId(defaultIdWorker.nextId());
        classConfigRangeDO.setBizId(userInfo.getId());
        classConfigRangeDO.setRangeType(rangeType);
        classConfigRangeDO.setRuleConfigId(punchClassConfigDTO.getId());
        classConfigRangeDO.setRuleConfigNo(punchClassConfigDTO.getConfigNo());
        classConfigRangeDO.setIsLatest(BusinessConstant.Y);
        classConfigRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
        classConfigRangeDO.setEffectTime(confirmDate);
        classConfigRangeDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        classConfigRangeDO.setRemark("员工入职绑定班次");
        BaseDOUtil.fillDOInsertBySystem(classConfigRangeDO);
        punchClassConfigRangeDao.save(classConfigRangeDO);

        //自动排班
        UserAutoShiftParam userAutoShiftParam = new UserAutoShiftParam();
        userAutoShiftParam.setClassNatureEnum(ClassNatureEnum.FIXED_CLASS);
        UserAutoShiftParam.PunchClassAddUserParam punchClassAddUserParam = new UserAutoShiftParam.PunchClassAddUserParam();
        punchClassAddUserParam.setUserIdList(Collections.singletonList(userInfo.getId()));
        punchClassAddUserParam.setTargetClassId(punchClassConfigDTO.getId());
        punchClassAddUserParam.setShiftStartDayId(confirmDayId);
        userAutoShiftParam.setPunchClassAddUserParam(punchClassAddUserParam);
        autoShiftConfigFactory.autoShift(userAutoShiftParam);
    }


    @Override
    protected void buildChangeClassRangeCount(Boolean isClassInfoUpdate,
                                              PunchClassConfigDTO oldDto,
                                              PunchClassConfigAddDTO newDto,
                                              PunchClassConfigUpdateConfirmVO result) {
        if (isClassInfoUpdate) {
            result.setItemInfoUpdate(Boolean.TRUE);
            if (CollectionUtils.isNotEmpty(oldDto.getClassConfigRangeList())) {
                List<Long> oldUserIds = oldDto.getClassConfigRangeList().stream().map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toList());
                result.setRangeEmployeeCount(oldUserIds.size());
            }
        }

        //班次适用范围变更
        //旧的班次范围是国家级别
        if (oldDto.countryLevel()) {
            buildOldCountryRangeDifferCount(oldDto, newDto, result);
            return;
        }

        //新的班次范围为国家级别
        if (newDto.countryLevel()) {
            buildNewCountryRangeDifferCount(oldDto, newDto, result);
            return;
        }

        //新旧班次适用范围都为非国家级别
        buildClassRangeDifferCount(oldDto, newDto, result);
    }

    @Override
    protected void bindNewClassRange(UserInfoDO userInfoDO, Date currentDate) {
        //添加到新的班次适用范围中
        PunchClassConfigDTO punchClassConfigDTO = punchClassConfigManage.selectTopPriorityByDeptIdOrCountry(userInfoDO.getDeptId(), userInfoDO.getLocationCountry());
        if (Objects.isNull(punchClassConfigDTO)) {
            return;
        }
        List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList = new ArrayList<>();
        String rangeType = Objects.equals(BusinessConstant.Y, punchClassConfigDTO.getIsCountryLevel()) ? RuleRangeTypeEnum.COUNTRY.getCode() : RuleRangeTypeEnum.DEPT.getCode();
        build(Collections.singletonList(userInfoDO.getId()), currentDate, rangeType, punchClassConfigDTO.getId(), punchClassConfigDTO.getConfigNo(), "员工班次性质切换班次绑定", addPunchClassConfigRangeDOList);
        punchClassConfigRangeDao.saveBatch(addPunchClassConfigRangeDOList);
    }

    @Override
    protected void bindNewClassRangeList(CalendarAndPunchHandlerDTO classHandlerDto,
                                         List<PunchClassConfigDTO> newPunchClassConfigList,
                                         List<PunchClassConfigRangeDO> addRunchClassConfigRangeList) {
        if (CollectionUtils.isEmpty(newPunchClassConfigList)) {
            return;
        }

        //固定班次优先绑定高优先级
        boolean isBindNewClass = false;
        List<PunchClassConfigDTO> deptRangeList = newPunchClassConfigList.stream()
                .filter(config -> !config.countryLevel() && StringUtils.isNotBlank(config.getDeptIds()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deptRangeList)) {
            for (PunchClassConfigDTO newRecord : deptRangeList) {
                if (!newRecord.convertDeptList().contains(classHandlerDto.getNewDeptId())) {
                    continue;
                }
                build(Collections.singletonList(classHandlerDto.getUserId()), new Date(), RuleRangeTypeEnum.DEPT.getCode(), newRecord.getId(), newRecord.getConfigNo(), "员工国家或部门变动", addRunchClassConfigRangeList);
                isBindNewClass = true;
            }
        }

        if (isBindNewClass) {
            return;
        }

        Optional<PunchClassConfigDTO> countryRangeConfigOptional = newPunchClassConfigList.stream()
                .filter(PunchClassConfigDTO::countryLevel).findFirst();
        if (countryRangeConfigOptional.isPresent()) {
            PunchClassConfigDTO classConfigDTO = countryRangeConfigOptional.get();
            build(Collections.singletonList(classHandlerDto.getUserId()), new Date(), RuleRangeTypeEnum.COUNTRY.getCode(), classConfigDTO.getId(), classConfigDTO.getConfigNo(), "员工国家或部门变动", addRunchClassConfigRangeList);
        }
    }


    @Override
    public PunchClassConfigAddVO customCheck(PunchClassConfigAddDTO addDTO) {
        PunchClassConfigAddVO result = new PunchClassConfigAddVO();
        List<PunchClassConfigAddVO.ClassRangeDuplicateInfoVO> classRangeDuplicateInfoVOList = new ArrayList<>();
        //适用范围重复性检测

        //用户级别
        if (CollectionUtils.isNotEmpty(addDTO.getUserIdList())) {
            List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectLatestByBizIdsAndRangeType(addDTO.getUserIdList(), RuleRangeTypeEnum.USER.getCode())
                    .stream()
                    .filter(range -> Objects.isNull(addDTO.getId()) || !Objects.equals(range.getRuleConfigId(), addDTO.getId()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(punchClassConfigRangeDOList)) {
                List<Long> userIds = punchClassConfigRangeDOList.stream().map(PunchClassConfigRangeDO::getBizId).distinct().collect(Collectors.toList());
                List<UserInfoDO> userInfoList = userInfoDao.getByUserIds(userIds);

                Map<Long, PunchClassConfigRangeDO> punchClassConfigRangeMap = punchClassConfigRangeDOList.stream().collect(Collectors.toMap(PunchClassConfigRangeDO::getBizId, Function.identity()));

                List<Long> ruleConfigIdList = punchClassConfigRangeDOList.stream().map(PunchClassConfigRangeDO::getRuleConfigId).distinct().collect(Collectors.toList());
                Map<Long, PunchClassConfigDO> punchClassConfigMap = punchClassConfigDao.selectByIds(ruleConfigIdList).stream().collect(Collectors.toMap(PunchClassConfigDO::getId, Function.identity()));

                List<PunchClassConfigAddVO.ClassRangeDuplicateInfoVO> classRangeDuplicateInfoList = userInfoList.stream().map(user -> {
                    PunchClassConfigAddVO.ClassRangeDuplicateInfoVO classRangeDuplicateInfoVO = new PunchClassConfigAddVO.ClassRangeDuplicateInfoVO();
                    classRangeDuplicateInfoVO.setRepeatName(user.getUserName());
                    PunchClassConfigRangeDO punchClassConfigRangeDO = punchClassConfigRangeMap.getOrDefault(user.getId(), new PunchClassConfigRangeDO());
                    PunchClassConfigDO punchClassConfigDO = punchClassConfigMap.getOrDefault(punchClassConfigRangeDO.getRuleConfigId(), new PunchClassConfigDO());
                    classRangeDuplicateInfoVO.setClassName(punchClassConfigDO.getClassName());
                    return classRangeDuplicateInfoVO;
                }).collect(Collectors.toList());
                classRangeDuplicateInfoVOList.addAll(classRangeDuplicateInfoList);
            }

        }

        //部门级别
        if (CollectionUtils.isNotEmpty(addDTO.getDeptIds())) {
            Map<Long, AttendanceDept> attendanceDeptMap = attendanceDeptService.listByDeptIds(addDTO.getDeptIds()).stream().collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));
            for (Long deptId : addDTO.getDeptIds()) {
                List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectLatestAndActiveByDeptId(deptId, addDTO.getClassNature())
                        .stream().filter(config -> Objects.isNull(addDTO.getId()) || !Objects.equals(config.getId(), addDTO.getId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(punchClassConfigDOList)) {
                    continue;
                }

                List<PunchClassConfigAddVO.ClassRangeDuplicateInfoVO> classRangeDuplicateInfoList = punchClassConfigDOList.stream().map(config -> {
                    PunchClassConfigAddVO.ClassRangeDuplicateInfoVO classRangeDuplicateInfoVO = new PunchClassConfigAddVO.ClassRangeDuplicateInfoVO();
                    String deptName;
                    AttendanceDept attendanceDept = attendanceDeptMap.getOrDefault(deptId, new AttendanceDept());
                    if (Locale.CHINA.equals(UserEvnHolder.getLocal())) {
                        deptName = attendanceDept.getDeptNameCn();
                    } else {
                        deptName = attendanceDept.getDeptNameEn();
                    }
                    classRangeDuplicateInfoVO.setRepeatName(deptName);
                    classRangeDuplicateInfoVO.setClassName(config.getClassName());
                    return classRangeDuplicateInfoVO;
                }).collect(Collectors.toList());
                classRangeDuplicateInfoVOList.addAll(classRangeDuplicateInfoList);
            }
        }

        //国家级别
        if (CollectionUtils.isEmpty(addDTO.getDeptIds()) && CollectionUtils.isEmpty(addDTO.getUserIdList())) {
            List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectLatestCountryRange(Collections.singletonList(addDTO.getCountry()), addDTO.getClassNature())
                    .stream()
                    .filter(config -> Objects.isNull(addDTO.getId()) || !Objects.equals(config.getId(), addDTO.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(punchClassConfigDOList)) {
                for (PunchClassConfigDO punchClassConfigDO : punchClassConfigDOList) {
                    PunchClassConfigAddVO.ClassRangeDuplicateInfoVO classRangeDuplicateInfoVO = new PunchClassConfigAddVO.ClassRangeDuplicateInfoVO();
                    classRangeDuplicateInfoVO.setRepeatName(punchClassConfigDO.getCountry());
                    classRangeDuplicateInfoVO.setClassName(punchClassConfigDO.getClassName());
                    classRangeDuplicateInfoVOList.add(classRangeDuplicateInfoVO);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(classRangeDuplicateInfoVOList)) {
            result.setSuccess(Boolean.FALSE);
            result.setClassRangeDuplicateInfoList(classRangeDuplicateInfoVOList);
        }

        return result;
    }

    @Override
    protected PunchClassConfigAddAutoShiftDTO buildPunchClassConfigAddAutoShiftDTO(PunchClassConfigAddDTO dto) {
        PunchClassConfigAddAutoShiftDTO result = new PunchClassConfigAddAutoShiftDTO();
        //国家级范围类型
        if (dto.countryLevel()) {
            List<UserInfoDO> userInfoDOList = userInfoManage.selectByCountryAndClassNature(dto.getCountry(), dto.getClassNature());
            if (CollectionUtils.isEmpty(userInfoDOList)) {
                return result;
            }
            List<Long> userIds = userInfoDOList.stream().map(UserInfoDO::getId).collect(Collectors.toList());
            List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectLatestAndActiveByBizIds(userIds);

            List<Long> bizIds = punchClassConfigRangeDOList.stream().map(PunchClassConfigRangeDO::getBizId).distinct().collect(Collectors.toList());
            Set<Long> waitShiftUserIds = userIds.stream().filter(userId -> !bizIds.contains(userId)).collect(Collectors.toSet());

            result.setClassId(dto.getId());
            result.setDayId(getDayIdByCountryTimeZone(dto.getCountry()));
            result.setNewRangeUserIdList(waitShiftUserIds);
            return result;
        }


        //待排班用户集合
        Set<Long> totalUserIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(dto.getUserIdList())) {
            totalUserIdList.addAll(dto.getUserIdList());
        }

        List<Long> deptRangeUserIds = dto.getDeptIds();
        if (CollectionUtils.isNotEmpty(deptRangeUserIds)) {
            List<Long> userIds = userInfoManage.selectByDeptIdsAndClassNature(dto.getCountry(), deptRangeUserIds, dto.getClassNature()).stream().map(UserInfoDO::getId).collect(Collectors.toList());
            //过滤存在用户级别的范围
            List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectLatestByBizIdsAndRangeType(userIds, RuleRangeTypeEnum.USER.getCode());
            List<Long> userRangeUserIds = punchClassConfigRangeDOList.stream().map(PunchClassConfigRangeDO::getBizId).distinct().collect(Collectors.toList());
            List<Long> filterUserIds = userIds.stream().filter(userId -> !userRangeUserIds.contains(userId)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterUserIds)) {
                totalUserIdList.addAll(filterUserIds);
            }
        }

        if (CollectionUtils.isEmpty(totalUserIdList)) {
            return result;
        }
        result.setClassId(dto.getId());
        result.setDayId(getDayIdByCountryTimeZone(dto.getCountry()));
        result.setNewRangeUserIdList(totalUserIdList);
        return result;
    }

    @Override
    protected void addPostProcessor(PunchClassConfigAddAutoShiftDTO classConfigAddAutoShiftDTO) {
        if (Objects.isNull(classConfigAddAutoShiftDTO)) {
            return;
        }
        punchClassConfigEventPublisher.sendPunchClassConfigAddEvent(classConfigAddAutoShiftDTO);
    }

    @Override
    protected void classRangeCustomCheck(PunchClassConfigDTO classConfigDTO, List<PunchClassConfigDTO> punchClassConfigDTOList) {
        if (CollectionUtils.isEmpty(punchClassConfigDTOList)) {
            return;
        }

        //国家级别
        if (classConfigDTO.countryLevel()) {
            List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectLatestCountryRange(Collections.singletonList(classConfigDTO.getCountry()), classConfigDTO.getClassNature());
            if (CollectionUtils.isEmpty(punchClassConfigDOList)) {
                return;
            }
            throw BusinessException.get(ErrorCodeEnum.CLASS_ENABLE_RANGE_DUPLICATE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_ENABLE_RANGE_DUPLICATE.getDesc()));
        }

        //部门级别
        List<Long> deptRangeList = classConfigDTO.convertDeptList();
        if (CollectionUtils.isNotEmpty(deptRangeList)) {
            for (Long deptId : deptRangeList) {
                List<PunchClassConfigDO> punchClassConfigList = punchClassConfigDao.selectLatestAndActiveByDeptId(deptId, classConfigDTO.getClassNature());
                if (CollectionUtils.isNotEmpty(punchClassConfigList)) {
                    throw BusinessException.get(ErrorCodeEnum.CLASS_ENABLE_RANGE_DUPLICATE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_ENABLE_RANGE_DUPLICATE.getDesc()));
                }
            }
        }

        if (CollectionUtils.isEmpty(classConfigDTO.getClassConfigRangeList())) {
            return;
        }

        //用户级别
        List<Long> userRangeList = classConfigDTO.getClassConfigRangeList().stream().filter(range -> Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType()))
                .map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userRangeList)) {
            List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectLatestByBizIdsAndRangeType(userRangeList, RuleRangeTypeEnum.USER.getCode());
            if (CollectionUtils.isNotEmpty(punchClassConfigRangeDOList)) {
                throw BusinessException.get(ErrorCodeEnum.CLASS_ENABLE_RANGE_DUPLICATE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_ENABLE_RANGE_DUPLICATE.getDesc()));
            }
        }


    }

    @Override
    protected void switchStatusPostProcessor(Long classId, Set<Long> userIds, String status) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        PunchClassConfigSwitchStatusDTO switchStatusDTO = PunchClassConfigSwitchStatusDTO.builder()
                .classId(classId)
                .userIds(userIds)
                .status(status)
                .build();
        punchClassConfigEventPublisher.sendPunchClassConfigSwitchStatusEvent(switchStatusDTO);
    }

    @Override
    protected Boolean checkIsOccupiedOtherSchedulingTasks(Set<Long> userIds, OperationTypeEnum operationTypeEnum) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Boolean.FALSE;
        }
        return shiftTaskService.checkUsersIsOccupiedByTask(new ArrayList<>(userIds));
    }

    @Override
    protected void classNatureSwitchPostProcessor(Long userId) {
        Map<Long, PunchClassConfigDO> punchClassConfigMap = punchClassConfigManage.selectTopPriorityByUserIds(Collections.singletonList(userId));
        if (MapUtils.isEmpty(punchClassConfigMap)) {
            return;
        }
        PunchClassConfigDO punchClassConfigDO = punchClassConfigMap.get(userId);
        UserAutoShiftParam userAutoShiftParam = new UserAutoShiftParam();
        userAutoShiftParam.setClassNatureEnum(ClassNatureEnum.FIXED_CLASS);
        UserAutoShiftParam.PunchClassAddUserParam punchClassAddUserParam = new UserAutoShiftParam.PunchClassAddUserParam();
        punchClassAddUserParam.setUserIdList(Collections.singletonList(userId));
        punchClassAddUserParam.setTargetClassId(punchClassConfigDO.getId());
        punchClassAddUserParam.setShiftStartDayId(getDayIdByCountryTimeZone(punchClassConfigDO.getCountry()));
        userAutoShiftParam.setPunchClassAddUserParam(punchClassAddUserParam);
        autoShiftConfigFactory.autoShift(userAutoShiftParam);
    }


    @Override
    protected void classConfigRangeUpdateBuild(Boolean isClassInfoUpdate,
                                               Date currentTime,
                                               Set<Long> removeUserIdList,
                                               PunchClassConfigDTO oldDto,
                                               PunchClassConfigAddDTO addDTO,
                                               PunchClassConfigDO classConfigDO,
                                               List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList,
                                               List<PunchClassConfigRangeDO> updatePunchClassConfigRangeDOList) {
        String remark = "页面编辑绑定班次";
        //班次时段没变，不需要升级班次
        Long classId = oldDto.getId();
        String classConfigCode = oldDto.getConfigNo();
        if (isClassInfoUpdate) {
            classId = classConfigDO.getId();
        }
        Map<Long, PunchClassConfigRangeDTO> oldRangeDTOList = Optional.ofNullable(oldDto.getClassConfigRangeList()).orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(PunchClassConfigRangeDTO::getBizId, Function.identity()));

        if (CollectionUtils.isNotEmpty(removeUserIdList)) {
            //移除人员降级绑定新班次
            downgradeBindingClassRangeList(oldDto.getClassNature(), removeUserIdList, currentTime, "班次编辑降级重新绑定", addPunchClassConfigRangeDOList);

            if (CollectionUtils.isNotEmpty(oldDto.getClassConfigRangeList())) {
                //移除的适用范围处理
                removeClassRangeBuild(currentTime, removeUserIdList, oldDto, updatePunchClassConfigRangeDOList);
            }
        }

        if (addDTO.countryLevel()) {
            List<Long> countryUserIds = userInfoManage.selectByCountryAndClassNature(addDTO.getCountry(), addDTO.getClassNature())
                    .stream().map(UserInfoDO::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(countryUserIds)) {
                return;
            }

            List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectLatestAndActiveByBizIds(countryUserIds);
            List<Long> userIds = punchClassConfigRangeDOList.stream()
                    .filter(range -> !Objects.equals(range.getRuleConfigId(), oldDto.getId()))
                    .map(PunchClassConfigRangeDO::getBizId).collect(Collectors.toList());
            countryUserIds.removeAll(userIds);

            punchClassConfigRangeDOList.stream().filter(range -> Objects.equals(range.getRuleConfigId(), oldDto.getId())).forEach(range -> {
                range.setIsLatest(BusinessConstant.N);
                range.setExpireTime(currentTime);
            });
            updatePunchClassConfigRangeDOList.addAll(punchClassConfigRangeDOList);

            build(countryUserIds, isClassInfoUpdate, currentTime, RuleRangeTypeEnum.COUNTRY.getCode(), classId, classConfigCode, remark, oldRangeDTOList,
                    addPunchClassConfigRangeDOList, updatePunchClassConfigRangeDOList);
            return;
        }

        Set<Long> userIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(addDTO.getUserIdList())) {
            userIdList.addAll(addDTO.getUserIdList());
            build(addDTO.getUserIdList(), isClassInfoUpdate, currentTime, RuleRangeTypeEnum.USER.getCode(), classId, classConfigCode, remark, oldRangeDTOList,
                    addPunchClassConfigRangeDOList, updatePunchClassConfigRangeDOList);

            List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectLatestAndActiveByBizIds(addDTO.getUserIdList());
            punchClassConfigRangeDOList.stream().filter(range -> !Objects.equals(range.getRangeType(), RuleRangeTypeEnum.USER.getCode())).forEach(range -> {
                range.setIsLatest(BusinessConstant.N);
                range.setExpireTime(currentTime);
            });
            updatePunchClassConfigRangeDOList.addAll(punchClassConfigRangeDOList);
        }

        if (CollectionUtils.isEmpty(addDTO.getDeptIds())) {
            return;
        }

        List<Long> userIds = userInfoManage.selectByDeptIdsAndClassNature(addDTO.getCountry(), addDTO.getDeptIds(), addDTO.getClassNature())
                .stream().map(UserInfoDO::getId).filter(id -> !userIdList.contains(id)).collect(Collectors.toList());

        List<Long> userRangeBizIdList = punchClassConfigRangeDao.selectLatestByBizIdsAndRangeType(userIds, RuleRangeTypeEnum.USER.getCode())
                .stream().map(PunchClassConfigRangeDO::getBizId).collect(Collectors.toList());

        userIds = userIds.stream().filter(user -> !userRangeBizIdList.contains(user)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectLatestAndActiveByBizIds(userIds);
        punchClassConfigRangeDOList.stream().filter(range -> !Objects.equals(range.getRuleConfigId(), oldDto.getId())).forEach(range -> {
            range.setIsLatest(BusinessConstant.N);
            range.setExpireTime(currentTime);
        });
        updatePunchClassConfigRangeDOList.addAll(punchClassConfigRangeDOList);

        build(userIds, isClassInfoUpdate, currentTime, RuleRangeTypeEnum.DEPT.getCode(), classId, classConfigCode, remark, oldRangeDTOList,
                addPunchClassConfigRangeDOList, updatePunchClassConfigRangeDOList);
    }

    @Override
    protected void classConfigRangeAddBuild(PunchClassConfigAddDTO addDTO,
                                            PunchClassConfigDO classConfigDO,
                                            List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList,
                                            List<PunchClassConfigRangeDO> updatePunchClassConfigRangeDOList,
                                            Date currentTime) {

        String remark = "页面新增绑定班次";
        if (addDTO.countryLevel()) {
            List<Long> countryUserIds = userInfoManage.selectByCountryAndClassNature(addDTO.getCountry(), addDTO.getClassNature())
                    .stream().map(UserInfoDO::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(countryUserIds)) {
                return;
            }

            List<Long> userIds = punchClassConfigRangeDao.selectLatestAndActiveByBizIds(countryUserIds).stream().map(PunchClassConfigRangeDO::getBizId).collect(Collectors.toList());
            countryUserIds.removeAll(userIds);

            build(countryUserIds, currentTime, RuleRangeTypeEnum.COUNTRY.getCode(), classConfigDO.getId(), classConfigDO.getConfigNo(), remark, addPunchClassConfigRangeDOList);
            return;
        }

        Set<Long> userIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(addDTO.getUserIdList())) {
            userIdList.addAll(addDTO.getUserIdList());
            build(addDTO.getUserIdList(), currentTime, RuleRangeTypeEnum.USER.getCode(), classConfigDO.getId(), classConfigDO.getConfigNo(), remark, addPunchClassConfigRangeDOList);

            List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectLatestAndActiveByBizIds(addDTO.getUserIdList());
            punchClassConfigRangeDOList.forEach(range -> {
                range.setIsLatest(BusinessConstant.N);
                range.setExpireTime(currentTime);
            });
            updatePunchClassConfigRangeDOList.addAll(punchClassConfigRangeDOList);
        }

        if (CollectionUtils.isEmpty(addDTO.getDeptIds())) {
            return;
        }

        List<Long> userIds = userInfoManage.selectByDeptIdsAndClassNature(addDTO.getCountry(), addDTO.getDeptIds(), addDTO.getClassNature())
                .stream().map(UserInfoDO::getId).filter(id -> !userIdList.contains(id)).collect(Collectors.toList());

        List<Long> userRangeBizIdList = punchClassConfigRangeDao.selectLatestByBizIdsAndRangeType(userIds, RuleRangeTypeEnum.USER.getCode())
                .stream().map(PunchClassConfigRangeDO::getBizId).collect(Collectors.toList());

        userIds = userIds.stream().filter(user -> !userRangeBizIdList.contains(user)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectLatestAndActiveByBizIds(userIds);
        punchClassConfigRangeDOList.forEach(range -> {
            range.setIsLatest(BusinessConstant.N);
            range.setExpireTime(currentTime);
        });
        updatePunchClassConfigRangeDOList.addAll(punchClassConfigRangeDOList);

        build(userIds, currentTime, RuleRangeTypeEnum.DEPT.getCode(), classConfigDO.getId(), classConfigDO.getConfigNo(), remark, addPunchClassConfigRangeDOList);
    }

    @Override
    protected PunchClassConfigRangeDifferDTO judgePunchClassConfigRangeUpdate(PunchClassConfigAddDTO newDto, PunchClassConfigDTO oldDto, Boolean isClassInfoUpdate) {
        PunchClassConfigRangeDifferDTO result = new PunchClassConfigRangeDifferDTO();

        //旧的班次范围是国家级别
        if (oldDto.countryLevel()) {
            return getOldCountryRangeDifferDTO(newDto, oldDto, result, isClassInfoUpdate);
        }

        //新的班次范围为国家级别
        if (newDto.countryLevel()) {
            return getNewCountryRangeDifferDTO(newDto, oldDto, result);
        }

        //新旧班次适用范围都为非国家级别
        return getClassConfigRangeDifferDTO(newDto, oldDto, isClassInfoUpdate, result);
    }


    @Override
    protected Set<Long> getClassRangeUserList(String country, String classNature, List<Long> userIds, List<Long> deptIds) {
        Set<Long> userIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            userIdList.addAll(userIds);
        }

        if (CollectionUtils.isNotEmpty(deptIds)) {
            Set<Long> deptRangeUserIds = userInfoManage.selectByDeptIdsAndClassNature(country, deptIds, classNature)
                    .stream().map(UserInfoDO::getId).filter(id -> !userIdList.contains(id)).collect(Collectors.toSet());

            Set<Long> userRangeBizIdList = punchClassConfigRangeDao.selectLatestByBizIdsAndRangeType(deptRangeUserIds, RuleRangeTypeEnum.USER.getCode())
                    .stream().map(PunchClassConfigRangeDO::getBizId).collect(Collectors.toSet());

            Set<Long> filterUserIds = deptRangeUserIds.stream().filter(userId -> !userRangeBizIdList.contains(userId)).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(filterUserIds)) {
                userIdList.addAll(filterUserIds);
            }

            return userIdList;
        }

        if (CollectionUtils.isEmpty(userIds) && CollectionUtils.isEmpty(deptIds)) {
            Set<Long> countryRangeUserIds = userInfoManage.selectByCountryAndClassNature(country, classNature).stream().map(UserInfoDO::getId).collect(Collectors.toSet());

            Set<Long> bizIds = punchClassConfigRangeDao.selectLatestAndActiveByBizIds(countryRangeUserIds)
                    .stream().map(PunchClassConfigRangeDO::getBizId).collect(Collectors.toSet());
            Set<Long> filterUserIds = countryRangeUserIds.stream().filter(userId -> !bizIds.contains(userId)).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(filterUserIds)) {
                userIdList.addAll(filterUserIds);
            }
        }
        return userIdList;
    }

    @Override
    protected void buildClassEnableRangeAddData(Date currentTime,
                                                PunchClassConfigDTO classConfigDTO,
                                                List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList) {
        String remark = "页面启用绑定班次";
        //国家级
        if (classConfigDTO.countryLevel()) {
            Set<Long> countryRangeUserIds = userInfoManage.selectByCountryAndClassNature(classConfigDTO.getCountry(), classConfigDTO.getClassNature())
                    .stream().map(UserInfoDO::getId).collect(Collectors.toSet());

            Set<Long> noCountryUserRangeBizIdList = punchClassConfigRangeDao.selectLatestAndActiveByBizIds(countryRangeUserIds)
                    .stream().map(PunchClassConfigRangeDO::getBizId).collect(Collectors.toSet());

            countryRangeUserIds.removeAll(noCountryUserRangeBizIdList);

            build(new ArrayList<>(countryRangeUserIds), currentTime, RuleRangeTypeEnum.COUNTRY.getCode(), classConfigDTO.getId(), classConfigDTO.getConfigNo(), remark, addPunchClassConfigRangeDOList);
            return;
        }

        if (CollectionUtils.isEmpty(classConfigDTO.getClassConfigRangeList())) {
            if (StringUtils.isBlank(classConfigDTO.getDeptIds())) {
                return;
            }
            //部门开始没挂人,后入职加入部门的人
            Set<Long> deptRangeUserIds = userInfoManage.selectByDeptIdsAndClassNature(classConfigDTO.getCountry(), classConfigDTO.convertDeptList(), classConfigDTO.getClassNature())
                    .stream().map(UserInfoDO::getId).collect(Collectors.toSet());

            Set<Long> userRangeBizIdList = punchClassConfigRangeDao.selectLatestByBizIdsAndRangeType(deptRangeUserIds, RuleRangeTypeEnum.USER.getCode())
                    .stream().map(PunchClassConfigRangeDO::getBizId).collect(Collectors.toSet());

            Set<Long> userIdList = deptRangeUserIds.stream().filter(userId -> !userRangeBizIdList.contains(userId)).collect(Collectors.toSet());
            build(new ArrayList<>(userIdList), currentTime, RuleRangeTypeEnum.DEPT.getCode(), classConfigDTO.getId(), classConfigDTO.getConfigNo(), remark, addPunchClassConfigRangeDOList);
            return;
        }

        Set<Long> userRangeSet = classConfigDTO.getClassConfigRangeList()
                .stream().filter(range -> Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType()))
                .map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toSet());
        build(new ArrayList<>(userRangeSet), currentTime, RuleRangeTypeEnum.USER.getCode(), classConfigDTO.getId(), classConfigDTO.getConfigNo(), remark, addPunchClassConfigRangeDOList);

        if (CollectionUtils.isNotEmpty(classConfigDTO.convertDeptList())) {
            Set<Long> deptRangeUserIds = userInfoManage.selectByDeptIdsAndClassNature(classConfigDTO.getCountry(), classConfigDTO.convertDeptList(), classConfigDTO.getClassNature())
                    .stream().map(UserInfoDO::getId).filter(userId -> !userRangeSet.contains(userId)).collect(Collectors.toSet());

            Set<Long> userRangeBizIdList = punchClassConfigRangeDao.selectLatestByBizIdsAndRangeType(deptRangeUserIds, RuleRangeTypeEnum.USER.getCode())
                    .stream().map(PunchClassConfigRangeDO::getBizId).collect(Collectors.toSet());

            Set<Long> userIdList = deptRangeUserIds.stream().filter(userId -> !userRangeBizIdList.contains(userId)).collect(Collectors.toSet());
            build(new ArrayList<>(userIdList), currentTime, RuleRangeTypeEnum.DEPT.getCode(), classConfigDTO.getId(), classConfigDTO.getConfigNo(), remark, addPunchClassConfigRangeDOList);
        }
    }

    private PunchClassConfigRangeDifferDTO getClassConfigRangeDifferDTO(PunchClassConfigAddDTO newDto,
                                                                        PunchClassConfigDTO oldDto,
                                                                        Boolean isClassInfoUpdate,
                                                                        PunchClassConfigRangeDifferDTO result) {
        boolean isRangeUpdate = false;
        List<Long> oldDeptIdList = StringUtils.isEmpty(oldDto.getDeptIds()) ? Collections.emptyList() : oldDto.convertDeptList();
        List<Long> newDeptIdList = Optional.ofNullable(newDto.getDeptIds()).orElse(Collections.emptyList());

        //移除的部门
        List<Long> removedDeptIdList = new ArrayList<>(oldDeptIdList);
        removedDeptIdList.removeAll(newDeptIdList);

        //新增的部门
        List<Long> addDeptIdList = new ArrayList<>(newDeptIdList);
        addDeptIdList.removeAll(oldDeptIdList);

        //不变的部门
        List<Long> mixDeptIdList = new ArrayList<>(oldDeptIdList);
        mixDeptIdList.retainAll(newDeptIdList);

        Set<Long> totalRemoveUserIdList = new HashSet<>();
        Set<Long> totalAddUserIdList = new HashSet<>();
        Set<Long> totalMixUserIdList = new HashSet<>();

        if (!removedDeptIdList.isEmpty() || !addDeptIdList.isEmpty()) {
            isRangeUpdate = Boolean.TRUE;
        }

        if (CollectionUtils.isNotEmpty(removedDeptIdList)) {
            setRangeChangeUserIds(newDto.getCountry(), newDto.getClassNature(), removedDeptIdList, totalRemoveUserIdList);
        }
        if (CollectionUtils.isNotEmpty(addDeptIdList)) {
            setRangeChangeUserIds(newDto.getCountry(), newDto.getClassNature(), addDeptIdList, totalAddUserIdList);
        }

        if (CollectionUtils.isNotEmpty(mixDeptIdList)) {
            setRangeChangeUserIds(newDto.getCountry(), newDto.getClassNature(), mixDeptIdList, totalMixUserIdList);
        }

        List<Long> oldUserIdList = Optional.ofNullable(oldDto.getClassConfigRangeList()).orElse(Collections.emptyList())
                .stream().filter(range -> Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType()))
                .map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toList());
        List<Long> newUserIdList = Optional.ofNullable(newDto.getUserIdList()).orElse(Collections.emptyList());

        //移除的用户
        List<Long> removedUserIdList = new ArrayList<>(oldUserIdList);
        removedUserIdList.removeAll(newUserIdList);

        //新增的用户
        List<Long> addUserIdList = new ArrayList<>(newUserIdList);
        addUserIdList.removeAll(oldUserIdList);

        //不变的用户
        List<Long> mixUserIdList = new ArrayList<>(oldUserIdList);
        mixUserIdList.retainAll(newUserIdList);

        if (!removedUserIdList.isEmpty() || !addUserIdList.isEmpty()) {
            isRangeUpdate = Boolean.TRUE;
        }

        totalRemoveUserIdList.addAll(removedUserIdList);
        totalAddUserIdList.addAll(addUserIdList);
        totalMixUserIdList.addAll(mixUserIdList);

        if (CollectionUtils.isNotEmpty(totalRemoveUserIdList)) {
            result.setRemoveRangeUserList(totalRemoveUserIdList);
        }

        if (CollectionUtils.isNotEmpty(totalMixUserIdList)) {
            result.setNoChangeRangeUserList(totalMixUserIdList);
        }

        if (CollectionUtils.isNotEmpty(totalAddUserIdList)) {
            result.setAddRangeUserList(totalAddUserIdList);
        }

        if (isRangeUpdate) {
            result.setRangeUpdate(isRangeUpdate);
        } else {
            if (isClassInfoUpdate) {
                Set<Long> rangeUserList = getClassRangeUserList(newDto.getCountry(), newDto.getClassNature(), newDto.getUserIdList(), newDto.getDeptIds());
                result.setNoChangeRangeUserList(rangeUserList);
            }
        }
        return result;
    }

    private PunchClassConfigRangeDifferDTO getNewCountryRangeDifferDTO(PunchClassConfigAddDTO newDto,
                                                                       PunchClassConfigDTO oldDto,
                                                                       PunchClassConfigRangeDifferDTO result) {
        //旧的班次适用范围人数
        Set<Long> oldUserIdSet = Optional.ofNullable(oldDto.getClassConfigRangeList()).orElse(Collections.emptyList())
                .stream().map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toSet());

        //新的规则适用范围人数
        Set<Long> countryRangeUserIds = userInfoManage.selectByCountryAndClassNature(newDto.getCountry(), newDto.getClassNature()).stream().map(UserInfoDO::getId).collect(Collectors.toSet());
        //过滤存在其他规则中的人
        Set<Long> bizIds = punchClassConfigRangeDao.selectLatestAndActiveByBizIds(countryRangeUserIds).stream()
                .filter(range -> !Objects.equals(range.getRuleConfigId(), oldDto.getId()))
                .map(PunchClassConfigRangeDO::getBizId)
                .collect(Collectors.toSet());
        countryRangeUserIds = countryRangeUserIds.stream().filter(userId -> !bizIds.contains(userId)).collect(Collectors.toSet());

        //移除的用户
        Collection<Long> removeUserIdList = CollectionUtils.subtract(oldUserIdSet, countryRangeUserIds);
        if (CollectionUtils.isNotEmpty(removeUserIdList)) {
            result.setRemoveRangeUserList(new HashSet<>(removeUserIdList));
        }

        //不变的用户
        List<Long> mixUserIdList = new ArrayList<>(oldUserIdSet);
        mixUserIdList.retainAll(countryRangeUserIds);
        if (CollectionUtils.isNotEmpty(mixUserIdList)) {
            result.setNoChangeRangeUserList(new HashSet<>(mixUserIdList));
        }

        //新增的用户
        List<Long> addUserIdList = new ArrayList<>(countryRangeUserIds);
        countryRangeUserIds.removeAll(oldUserIdSet);
        if (CollectionUtils.isNotEmpty(addUserIdList)) {
            result.setAddRangeUserList(new HashSet<>(addUserIdList));
        }

        result.setRangeUpdate(Boolean.TRUE);
        return result;
    }

    private PunchClassConfigRangeDifferDTO getOldCountryRangeDifferDTO(PunchClassConfigAddDTO newDto,
                                                                       PunchClassConfigDTO oldDto,
                                                                       PunchClassConfigRangeDifferDTO result,
                                                                       Boolean isClassInfoUpdate) {
        if (newDto.countryLevel()) {
            if (isClassInfoUpdate) {
                Set<Long> rangeUserList = getClassRangeUserList(newDto.getCountry(), newDto.getClassNature(), newDto.getUserIdList(), newDto.getDeptIds());
                result.setNoChangeRangeUserList(rangeUserList);
            }
            return result;
        }

        Set<Long> oldCountryRangeUserIds = Optional.ofNullable(oldDto.getClassConfigRangeList()).orElse(Collections.emptyList())
                .stream().map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toSet());

        Set<Long> userIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(newDto.getUserIdList())) {
            userIdList.addAll(newDto.getUserIdList());
        }

        if (CollectionUtils.isNotEmpty(newDto.getDeptIds())) {
            List<Long> userIds = userInfoManage.selectByDeptIdsAndClassNature(newDto.getCountry(), newDto.getDeptIds(), newDto.getClassNature())
                    .stream().map(UserInfoDO::getId).filter(id -> !userIdList.contains(id)).collect(Collectors.toList());

            List<Long> userRangeBizIdList = punchClassConfigRangeDao.selectLatestByBizIdsAndRangeType(userIds, RuleRangeTypeEnum.USER.getCode())
                    .stream().map(PunchClassConfigRangeDO::getBizId).collect(Collectors.toList());

            List<Long> filterUserIds = userIds.stream().filter(userId -> !userRangeBizIdList.contains(userId)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterUserIds)) {
                userIdList.addAll(filterUserIds);
            }
        }

        //可能存在用户级别的人不属于当前国家
        Collection<Long> addUserIdList = CollectionUtils.subtract(userIdList, oldCountryRangeUserIds);

        if (CollectionUtils.isNotEmpty(addUserIdList)) {
            result.setAddRangeUserList(new HashSet<>(addUserIdList));
        }

        //不变的用户
        List<Long> mixUserIdList = new ArrayList<>(oldCountryRangeUserIds);
        mixUserIdList.retainAll(userIdList);
        if (CollectionUtils.isNotEmpty(mixUserIdList)) {
            result.setNoChangeRangeUserList(new HashSet<>(mixUserIdList));
        }

        //移除的用户
        List<Long> removedUserIdList = new ArrayList<>(oldCountryRangeUserIds);
        removedUserIdList.removeAll(userIdList);
        if (CollectionUtils.isNotEmpty(removedUserIdList)) {
            result.setRemoveRangeUserList(new HashSet<>(removedUserIdList));
        }

        result.setRangeUpdate(Boolean.TRUE);
        return result;
    }

    private void buildClassRangeDifferCount(PunchClassConfigDTO oldDto,
                                            PunchClassConfigAddDTO newDto,
                                            PunchClassConfigUpdateConfirmVO result) {
        boolean isRangeUpdate = false;
        List<Long> oldDeptIdList = StringUtils.isEmpty(oldDto.getDeptIds()) ? Collections.emptyList() : oldDto.convertDeptList();
        List<Long> newDeptIdList = Optional.ofNullable(newDto.getDeptIds()).orElse(Collections.emptyList());

        //移除的部门
        List<Long> removedDeptIdList = new ArrayList<>(oldDeptIdList);
        removedDeptIdList.removeAll(newDeptIdList);

        //新增的部门
        List<Long> addDeptIdList = new ArrayList<>(newDeptIdList);
        addDeptIdList.removeAll(oldDeptIdList);

        Set<Long> totalRemoveUserIdList = new HashSet<>();
        Set<Long> totalAddUserIdList = new HashSet<>();

        if (!removedDeptIdList.isEmpty() || !addDeptIdList.isEmpty()) {
            isRangeUpdate = true;
        }

        if (CollectionUtils.isNotEmpty(removedDeptIdList)) {
            setRangeChangeUserIds(newDto.getCountry(), newDto.getClassNature(), removedDeptIdList, totalRemoveUserIdList);
        }
        if (CollectionUtils.isNotEmpty(addDeptIdList)) {
            setRangeChangeUserIds(newDto.getCountry(), newDto.getClassNature(), addDeptIdList, totalAddUserIdList);
        }

        List<Long> oldUserIdList = Optional.ofNullable(oldDto.getClassConfigRangeList()).orElse(Collections.emptyList())
                .stream().filter(range -> Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType()))
                .map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toList());
        List<Long> newUserIdList = Optional.ofNullable(newDto.getUserIdList()).orElse(Collections.emptyList());

        //移除的用户
        List<Long> removedUserIdList = new ArrayList<>(oldUserIdList);
        removedUserIdList.removeAll(newUserIdList);
        totalRemoveUserIdList.addAll(removedUserIdList);

        //新增的用户
        List<Long> addUserIdList = new ArrayList<>(newUserIdList);
        addUserIdList.removeAll(oldUserIdList);
        totalAddUserIdList.addAll(addUserIdList);

        if (!removedUserIdList.isEmpty() || !addUserIdList.isEmpty()) {
            isRangeUpdate = true;
        }

        if (CollectionUtils.isNotEmpty(totalRemoveUserIdList)) {
            result.setRemoveRangeEmployeeCount(totalRemoveUserIdList.size());
        }

        if (CollectionUtils.isNotEmpty(totalAddUserIdList)) {
            result.setAddRangeEmployeeCount(totalAddUserIdList.size());
        }
        result.setRangeUpdate(isRangeUpdate);
    }

    private void buildNewCountryRangeDifferCount(PunchClassConfigDTO oldDto,
                                                 PunchClassConfigAddDTO newDto,
                                                 PunchClassConfigUpdateConfirmVO result) {
        //旧的班次适用范围人数
        Set<Long> oldUserIdSet = Optional.ofNullable(oldDto.getClassConfigRangeList()).orElse(Collections.emptyList())
                .stream().map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toSet());

        //新的规则适用范围人数
        Set<Long> countryRangeUserIds = userInfoManage.selectByCountryAndClassNature(newDto.getCountry(), newDto.getClassNature()).stream().map(UserInfoDO::getId).collect(Collectors.toSet());
        //过滤存在其他规则中的人
        Set<Long> bizIds = punchClassConfigRangeDao.selectLatestAndActiveByBizIds(countryRangeUserIds).stream()
                .filter(range -> !Objects.equals(range.getRuleConfigId(), oldDto.getId()))
                .map(PunchClassConfigRangeDO::getBizId)
                .collect(Collectors.toSet());
        countryRangeUserIds = countryRangeUserIds.stream().filter(userId -> !bizIds.contains(userId)).collect(Collectors.toSet());

        result.setRemoveRangeEmployeeCount(oldUserIdSet.size());
        result.setAddRangeEmployeeCount(countryRangeUserIds.size());
        result.setRangeUpdate(Boolean.TRUE);
    }

    private void buildOldCountryRangeDifferCount(PunchClassConfigDTO oldDto,
                                                 PunchClassConfigAddDTO newDto,
                                                 PunchClassConfigUpdateConfirmVO result) {
        if (newDto.countryLevel()) {
            return;
        }

        Set<Long> oldCountryRangeUserIds = Optional.ofNullable(oldDto.getClassConfigRangeList()).orElse(Collections.emptyList())
                .stream().map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toSet());

        Set<Long> userIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(newDto.getUserIdList())) {
            userIdList.addAll(newDto.getUserIdList());
        }

        if (CollectionUtils.isNotEmpty(newDto.getDeptIds())) {
            List<Long> userIds = userInfoManage.selectByDeptIdsAndClassNature(newDto.getCountry(), newDto.getDeptIds(), newDto.getClassNature())
                    .stream().map(UserInfoDO::getId).filter(id -> !userIdList.contains(id)).collect(Collectors.toList());

            List<Long> userRangeBizIdList = punchClassConfigRangeDao.selectLatestByBizIdsAndRangeType(userIds, RuleRangeTypeEnum.USER.getCode())
                    .stream().map(PunchClassConfigRangeDO::getBizId).collect(Collectors.toList());

            List<Long> filterUserIds = userIds.stream().filter(userId -> !userRangeBizIdList.contains(userId)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterUserIds)) {
                userIdList.addAll(filterUserIds);
            }
        }

        //两者交集表示规则编辑保留的人
        Collection<Long> retainUserIdList = CollectionUtils.intersection(userIdList, oldCountryRangeUserIds);

        //可能存在用户级别的人不属于当前国家
        Collection<Long> addUserIdList = CollectionUtils.subtract(userIdList, oldCountryRangeUserIds);

        if (CollectionUtils.isNotEmpty(oldCountryRangeUserIds)) {
            result.setRemoveRangeEmployeeCount(oldCountryRangeUserIds.size());
        }

        if (CollectionUtils.isNotEmpty(retainUserIdList) || CollectionUtils.isNotEmpty(addUserIdList)) {
            result.setAddRangeEmployeeCount(retainUserIdList.size() + addUserIdList.size());
        }
        result.setRangeUpdate(Boolean.TRUE);
    }


}
