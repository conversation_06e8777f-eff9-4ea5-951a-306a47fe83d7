package com.imile.attendance.rule;

import com.google.common.collect.Sets;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.infrastructure.logRecord.LogContentService;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.util.BigDecimalUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 规则变更日志服务类
 * <p>
 * 该服务负责生成各类考勤规则配置变更的日志记录，包括：
 * 1. 打卡规则（PunchConfig）变更日志
 * 2. 补卡规则（ReissueCardConfig）变更日志
 * 3. 加班规则（OverTimeConfig）变更日志
 * <p>
 * 日志内容包括规则基本信息的变更（如名称、类型等）以及适用范围的变更（适用部门和适用员工）。
 * 对于适用范围的变更，会同时展示变更前后的完整列表，以及具体新增和移除的详细信息，提高日志可读性。
 * <p>
 * 示例日志格式：
 * 修改打卡规则: [OMN]的[5.12-1]
 * 打卡规则类型:[FIXED_WORK]更新为[NO_NEED_WORK];
 * [适用部门]：[]更新为[HQ 高管群体]
 * [适用员工]：[]更新为[张三(A001),李四(A002)]
 *
 * <AUTHOR> chen
 * @Date 2025/5/14
 */
@Service
public class RuleChangeLogService {

    @Resource
    private LogContentService logContentService;
    @Resource
    private PunchConfigRangeDao punchConfigRangeDao;
    @Resource
    private ReissueCardConfigRangeDao reissueCardConfigRangeDao;
    @Resource
    private OverTimeConfigRangeDao overTimeConfigRangeDao;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceDeptService deptService;

    /**
     * 没有范围的标记，用于在日志中表示空的适用范围
     */
    private static final String NOT_HAVE_RANGE_FLAG = "-";

    /**
     * 格式化BigDecimal类型的时间值，去除尾随的零
     *
     * @param time BigDecimal类型的时间值
     * @return 格式化后的时间字符串，如果输入为null则返回空字符串
     */
    private String formatBigDecimalTime(BigDecimal time) {
        if (Objects.isNull(time)) {
            return "";
        } else {
            return time.stripTrailingZeros().toPlainString();
        }
    }


    /**
     * 将用户ID转换为用户信息字符串，格式为“用户名称(用户编码)”
     *
     * @param userId 用户ID
     * @param userMap 用户信息映射
     * @return 用户信息字符串
     */
    private String formatUserInfo(Long userId, Map<Long, AttendanceUser> userMap) {
        AttendanceUser user = userMap.get(userId);
        if (user == null) {
            return String.valueOf(userId); // 如果找不到用户信息，返回用户ID
        }
        return user.getUserName() + "(" + user.getUserCode() + ")";
    }

    /**
     * 将部门ID转换为部门名称
     *
     * @param deptId 部门ID
     * @param deptMap 部门信息映射
     * @return 部门名称
     */
    private String formatDeptInfo(Long deptId, Map<Long, AttendanceDept> deptMap) {
        AttendanceDept dept = deptMap.get(deptId);
        if (dept == null) {
            return String.valueOf(deptId); // 如果找不到部门信息，返回部门ID
        }
        return dept.getLocalizeName();
    }

    /**
     * 构建打卡规则变更日志
     * <p>
     * 比较新旧打卡规则配置，生成详细的变更日志，包括：
     * 1. 规则名称变更
     * 2. 规则类型变更
     * 3. 上下班打卡时间间隔变更
     * 4. 适用部门范围变更（包括完整列表对比和新增/移除明细）
     * 5. 适用员工范围变更（包括完整列表对比和新增/移除明细）
     *
     * @param newPunchConfig 新的打卡规则配置
     * @param currentConfig 当前打卡规则配置
     * @return 格式化的变更日志字符串，如果输入参数为null则返回null
     */
    public String buildPunchConfigChangeLog(PunchConfigDO newPunchConfig,
                                            PunchConfigDO currentConfig) {
        if (newPunchConfig == null || currentConfig == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        String title = logContentService.commonLogContent(currentConfig.getCountry(), currentConfig.getConfigName());
        sb.append(title).append("\n");
        if (!StringUtils.equals(newPunchConfig.getConfigName(), currentConfig.getConfigName())) {
            sb.append("打卡规则名称:")
                    .append("[").append(currentConfig.getConfigName()).append("]")
                    .append("更新为")
                    .append("[").append(newPunchConfig.getConfigName()).append("]").append("\n");
        }
        if (!StringUtils.equals(newPunchConfig.getConfigType(), currentConfig.getConfigType())) {
            sb.append("打卡规则类型:")
                    .append("[").append(PunchConfigTypeEnum.getInstance(currentConfig.getConfigType())).append("]")
                    .append("更新为")
                    .append("[").append(PunchConfigTypeEnum.getInstance(newPunchConfig.getConfigType())).append("]").append("\n");
        }
        if (Objects.nonNull(newPunchConfig.getPunchTimeInterval()) &&
                BigDecimalUtil.compare(newPunchConfig.getPunchTimeInterval(), currentConfig.getPunchTimeInterval()) != 0) {
            sb.append("上下班打卡时间间隔:")
                    .append("[").append(formatBigDecimalTime(currentConfig.getPunchTimeInterval())).append("]")
                    .append("更新为")
                    .append("[").append(formatBigDecimalTime(newPunchConfig.getPunchTimeInterval())).append("]").append("\n");
        }
        // 处理部门适用范围变更日志
        Set<Long> newDeptSet = new HashSet<>(newPunchConfig.listDeptIds());
        Set<Long> currentDeptSet = new HashSet<>(currentConfig.listDeptIds());

        // 计算部门差异集
        // 新增的部门 = 新集合 - 旧集合
        Set<Long> addedDeptSet = new HashSet<>(newDeptSet);
        addedDeptSet.removeAll(currentDeptSet);
        // 移除的部门 = 旧集合 - 新集合
        Set<Long> removedDeptSet = new HashSet<>(currentDeptSet);
        removedDeptSet.removeAll(newDeptSet);

        // 如果有任何变化，则记录日志
        if (!addedDeptSet.isEmpty() || !removedDeptSet.isEmpty()) {
            // 获取所有相关部门信息（新旧部门的并集）
            Map<Long, AttendanceDept> deptMap = deptService.listByDeptIds(Sets.union(newDeptSet, currentDeptSet))
                    .stream()
                    .collect(Collectors.toMap(AttendanceDept::getId, Function.identity(), (a, b) -> a)); // 如果有重复键，保留第一个

            // 将部门ID转换为部门名称字符串
            // 如果集合为空，则使用特殊标记；否则将每个部门ID转换为部门名称，并用逗号连接
            String currentDeptInfo = currentDeptSet.isEmpty() ? NOT_HAVE_RANGE_FLAG : currentDeptSet.stream()
                    .map(deptId -> formatDeptInfo(deptId, deptMap))
                    .collect(Collectors.joining(","));
            String newDeptInfo = newDeptSet.isEmpty() ? NOT_HAVE_RANGE_FLAG : newDeptSet.stream()
                    .map(deptId -> formatDeptInfo(deptId, deptMap))
                    .collect(Collectors.joining(","));

            sb.append("适用部门:")
                    .append("[").append(currentDeptInfo).append("]")
                    .append("更新为")
                    .append("[").append(newDeptInfo).append("]").append("\n");

            // 添加更详细的变更信息
            if (!addedDeptSet.isEmpty()) {
                String addedDeptInfo = addedDeptSet.stream()
                        .map(deptId -> formatDeptInfo(deptId, deptMap))
                        .collect(Collectors.joining(","));
                sb.append("  - 新增:[").append(addedDeptInfo).append("]\n");
            }
            if (!removedDeptSet.isEmpty()) {
                String removedDeptInfo = removedDeptSet.stream()
                        .map(deptId -> formatDeptInfo(deptId, deptMap))
                        .collect(Collectors.joining(","));
                sb.append("  - 移除:[").append(removedDeptInfo).append("]\n");
            }
        }
        // 处理人员适用范围变更日志
        Set<Long> newUserSet = punchConfigRangeDao.listActivedConfigByConfigId(newPunchConfig.getId())
                .stream()
                .filter(PunchConfigRangeDO::areUserRange)
                .map(PunchConfigRangeDO::getBizId)
                .collect(Collectors.toSet());
        Set<Long> currentUserSet = punchConfigRangeDao.listActivedConfigByConfigId(currentConfig.getId())
                .stream()
                .filter(PunchConfigRangeDO::areUserRange)
                .map(PunchConfigRangeDO::getBizId)
                .collect(Collectors.toSet());
        // 计算人员差异集
        // 新增的人员 = 新集合 - 旧集合
        Set<Long> added = new HashSet<>(newUserSet);
        added.removeAll(currentUserSet);
        // 移除的人员 = 旧集合 - 新集合
        Set<Long> removed = new HashSet<>(currentUserSet);
        removed.removeAll(newUserSet);

        // 如果有任何变化，则记录日志
        if (!added.isEmpty() || !removed.isEmpty()) {
            // 获取所有相关用户信息（新旧用户的并集）
            Map<Long, AttendanceUser> userMap = userService.listUsersByIds(Sets.union(currentUserSet, newUserSet))
                    .stream()
                    .collect(Collectors.toMap(AttendanceUser::getId, Function.identity(), (a, b) -> a)); // 如果有重复键，保留第一个

            // 将用户ID转换为用户信息字符串
            // 如果集合为空，则使用特殊标记；否则将每个用户ID转换为"用户名称(用户编码)"格式，并用逗号连接
            String currentUserInfo = currentUserSet.isEmpty() ? NOT_HAVE_RANGE_FLAG : currentUserSet.stream()
                    .map(userId -> formatUserInfo(userId, userMap))
                    .collect(Collectors.joining(","));
            String newUserInfo = newUserSet.isEmpty() ? NOT_HAVE_RANGE_FLAG : newUserSet.stream()
                    .map(userId -> formatUserInfo(userId, userMap))
                    .collect(Collectors.joining(","));

            sb.append("适用员工:")
                    .append("[").append(currentUserInfo).append("]")
                    .append("更新为")
                    .append("[").append(newUserInfo).append("]").append("\n");

            // 添加更详细的变更信息
            if (!added.isEmpty()) {
                String addedUserInfo = added.stream()
                        .map(userId -> formatUserInfo(userId, userMap))
                        .collect(Collectors.joining(","));
                sb.append("  - 新增:[").append(addedUserInfo).append("]\n");
            }
            if (!removed.isEmpty()) {
                String removedUserInfo = removed.stream()
                        .map(userId -> formatUserInfo(userId, userMap))
                        .collect(Collectors.joining(","));
                sb.append("  - 移除:[").append(removedUserInfo).append("]\n");
            }
        }
        return sb.toString();
    }

    /**
     * 构建补卡规则变更日志
     * <p>
     * 比较新旧补卡规则配置，生成详细的变更日志，包括：
     * 1. 规则名称变更
     * 2. 每月最大补卡次数变更
     * 3. 适用部门范围变更（包括完整列表对比和新增/移除明细）
     * 4. 适用员工范围变更（包括完整列表对比和新增/移除明细）
     *
     * @param newReissueCardConfig 新的补卡规则配置
     * @param currentConfig 当前补卡规则配置
     * @return 格式化的变更日志字符串，如果输入参数为null则返回null
     */
    public String buildReissueConfigChangeLog(ReissueCardConfigDO newReissueCardConfig,
                                              ReissueCardConfigDO currentConfig) {
        if (newReissueCardConfig == null || currentConfig == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        String title = logContentService.commonLogContent(currentConfig.getCountry(), currentConfig.getConfigName());
        sb.append(title).append("\n");
        if (!StringUtils.equals(newReissueCardConfig.getConfigName(), currentConfig.getConfigName())) {
            sb.append("补卡规则名称:")
                    .append("[").append(currentConfig.getConfigName()).append("]")
                    .append("更新为")
                    .append("[").append(newReissueCardConfig.getConfigName()).append("]").append("\n");
        }
        if (Objects.nonNull(newReissueCardConfig.getMaxRepunchNumber()) &&
                newReissueCardConfig.getMaxRepunchNumber().compareTo(currentConfig.getMaxRepunchNumber()) != 0) {
            sb.append("每月最大补卡次数:")
                    .append("[").append(currentConfig.getMaxRepunchNumber()).append("]")
                    .append("更新为")
                    .append("[").append(newReissueCardConfig.getMaxRepunchNumber()).append("]").append("\n");
        }

        // 处理部门适用范围变更日志
        Set<Long> newDeptSet = new HashSet<>(newReissueCardConfig.listDeptIds());
        Set<Long> currentDeptSet = new HashSet<>(currentConfig.listDeptIds());

        // 计算部门差异集
        // 新增的部门 = 新集合 - 旧集合
        Set<Long> addedDeptSet = new HashSet<>(newDeptSet);
        addedDeptSet.removeAll(currentDeptSet);
        // 移除的部门 = 旧集合 - 新集合
        Set<Long> removedDeptSet = new HashSet<>(currentDeptSet);
        removedDeptSet.removeAll(newDeptSet);

        // 如果有任何变化，则记录日志
        if (!addedDeptSet.isEmpty() || !removedDeptSet.isEmpty()) {
            // 获取所有相关部门信息（新旧部门的并集）
            Map<Long, AttendanceDept> deptMap = deptService.listByDeptIds(Sets.union(newDeptSet, currentDeptSet))
                    .stream()
                    .collect(Collectors.toMap(AttendanceDept::getId, Function.identity(), (a, b) -> a)); // 如果有重复键，保留第一个

            // 将部门ID转换为部门名称字符串
            // 如果集合为空，则使用特殊标记；否则将每个部门ID转换为部门名称，并用逗号连接
            String currentDeptInfo = currentDeptSet.isEmpty() ? NOT_HAVE_RANGE_FLAG : currentDeptSet.stream()
                    .map(deptId -> formatDeptInfo(deptId, deptMap))
                    .collect(Collectors.joining(","));
            String newDeptInfo = newDeptSet.isEmpty() ? NOT_HAVE_RANGE_FLAG : newDeptSet.stream()
                    .map(deptId -> formatDeptInfo(deptId, deptMap))
                    .collect(Collectors.joining(","));

            sb.append("适用部门:")
                    .append("[").append(currentDeptInfo).append("]")
                    .append("更新为")
                    .append("[").append(newDeptInfo).append("]").append("\n");

            // 添加更详细的变更信息
            if (!addedDeptSet.isEmpty()) {
                String addedDeptInfo = addedDeptSet.stream()
                        .map(deptId -> formatDeptInfo(deptId, deptMap))
                        .collect(Collectors.joining(","));
                sb.append("  - 新增:[").append(addedDeptInfo).append("]\n");
            }
            if (!removedDeptSet.isEmpty()) {
                String removedDeptInfo = removedDeptSet.stream()
                        .map(deptId -> formatDeptInfo(deptId, deptMap))
                        .collect(Collectors.joining(","));
                sb.append("  - 移除:[").append(removedDeptInfo).append("]\n");
            }
        }

        // 处理人员适用范围变更日志
        Set<Long> newUserSet = reissueCardConfigRangeDao.listActivedConfigByConfigId(newReissueCardConfig.getId())
                .stream()
                .filter(ReissueCardConfigRangeDO::areUserRange)
                .map(ReissueCardConfigRangeDO::getBizId)
                .collect(Collectors.toSet());
        Set<Long> currentUserSet = reissueCardConfigRangeDao.listActivedConfigByConfigId(currentConfig.getId())
                .stream()
                .filter(ReissueCardConfigRangeDO::areUserRange)
                .map(ReissueCardConfigRangeDO::getBizId)
                .collect(Collectors.toSet());

        // 计算人员差异集
        // 新增的人员 = 新集合 - 旧集合
        Set<Long> added = new HashSet<>(newUserSet);
        added.removeAll(currentUserSet);
        // 移除的人员 = 旧集合 - 新集合
        Set<Long> removed = new HashSet<>(currentUserSet);
        removed.removeAll(newUserSet);

        // 如果有任何变化，则记录日志
        if (!added.isEmpty() || !removed.isEmpty()) {
            // 获取所有相关用户信息（新旧用户的并集）
            Map<Long, AttendanceUser> userMap = userService.listUsersByIds(Sets.union(currentUserSet, newUserSet))
                    .stream()
                    .collect(Collectors.toMap(AttendanceUser::getId, Function.identity(), (a, b) -> a)); // 如果有重复键，保留第一个

            // 将用户ID转换为用户信息字符串
            // 如果集合为空，则使用特殊标记；否则将每个用户ID转换为"用户名称(用户编码)"格式，并用逗号连接
            String currentUserInfo = currentUserSet.isEmpty() ? NOT_HAVE_RANGE_FLAG : currentUserSet.stream()
                    .map(userId -> formatUserInfo(userId, userMap))
                    .collect(Collectors.joining(","));
            String newUserInfo = newUserSet.isEmpty() ? NOT_HAVE_RANGE_FLAG : newUserSet.stream()
                    .map(userId -> formatUserInfo(userId, userMap))
                    .collect(Collectors.joining(","));

            sb.append("适用员工:")
                    .append("[").append(currentUserInfo).append("]")
                    .append("更新为")
                    .append("[").append(newUserInfo).append("]").append("\n");

            // 添加更详细的变更信息
            if (!added.isEmpty()) {
                String addedUserInfo = added.stream()
                        .map(userId -> formatUserInfo(userId, userMap))
                        .collect(Collectors.joining(","));
                sb.append("  - 新增:[").append(addedUserInfo).append("]\n");
            }
            if (!removed.isEmpty()) {
                String removedUserInfo = removed.stream()
                        .map(userId -> formatUserInfo(userId, userMap))
                        .collect(Collectors.joining(","));
                sb.append("  - 移除:[").append(removedUserInfo).append("]\n");
            }
        }
        return sb.toString();
    }

    /**
     * 构建加班规则变更日志
     * <p>
     * 比较新旧加班规则配置，生成详细的变更日志，包括：
     * 1. 规则名称变更
     * 2. 工作加班开始时间变更
     * 3. 工作日最长有效加班时间变更
     * 4. 工作日加班补贴方式变更
     * 5. 休息日最长有效加班时间变更
     * 6. 休息日加班补贴方式变更
     * 7. 节假日最长有效加班时间变更
     * 8. 节假日加班补贴方式变更
     * 9. 适用部门范围变更（包括完整列表对比和新增/移除明细）
     * 10. 适用员工范围变更（包括完整列表对比和新增/移除明细）
     *
     * @param newOverTimeConfig 新的加班规则配置
     * @param currentConfig 当前加班规则配置
     * @return 格式化的变更日志字符串，如果输入参数为null则返回null
     */
    public String buildOverTimeConfigChangeLog(OverTimeConfigDO newOverTimeConfig,
                                               OverTimeConfigDO currentConfig) {
        if (newOverTimeConfig == null || currentConfig == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        String title = logContentService.commonLogContent(currentConfig.getCountry(), currentConfig.getConfigName());
        sb.append(title).append("\n");
        if (!StringUtils.equals(newOverTimeConfig.getConfigName(), currentConfig.getConfigName())) {
            sb.append("加班规则名称:")
                    .append("[").append(currentConfig.getConfigName()).append("]")
                    .append("更新为")
                    .append("[").append(newOverTimeConfig.getConfigName()).append("]").append("\n");
        }
        if (Objects.nonNull(newOverTimeConfig.getWorkingOutStartTime()) &&
                newOverTimeConfig.getWorkingOutStartTime().compareTo(currentConfig.getWorkingOutStartTime()) != 0) {
            sb.append("工作加班开始时间:")
                    .append("[").append(formatBigDecimalTime(currentConfig.getWorkingOutStartTime())).append("]")
                    .append("更新为")
                    .append("[").append(formatBigDecimalTime(newOverTimeConfig.getWorkingOutStartTime())).append("]").append("\n");
        }
        if (Objects.nonNull(newOverTimeConfig.getWorkingEffectiveTime()) &&
                newOverTimeConfig.getWorkingEffectiveTime().compareTo(currentConfig.getWorkingEffectiveTime()) != 0) {
            sb.append("工作日最长有效加班时间:")
                    .append("[").append(formatBigDecimalTime(currentConfig.getWorkingEffectiveTime())).append("]")
                    .append("更新为")
                    .append("[").append(formatBigDecimalTime(newOverTimeConfig.getWorkingEffectiveTime())).append("]").append("\n");
        }
        if (!StringUtils.equals(newOverTimeConfig.getWorkingSubsidyType(), currentConfig.getWorkingSubsidyType())) {
            sb.append("工作日加班补贴方式:")
                    .append("[").append(currentConfig.getWorkingSubsidyType()).append("]")
                    .append("更新为")
                    .append("[").append(newOverTimeConfig.getWorkingSubsidyType()).append("]").append("\n");
        }
        if (Objects.nonNull(newOverTimeConfig.getRestEffectiveTime()) &&
                newOverTimeConfig.getRestEffectiveTime().compareTo(currentConfig.getRestEffectiveTime()) != 0) {
            sb.append("休息日最长有效加班时间:")
                    .append("[").append(formatBigDecimalTime(currentConfig.getRestEffectiveTime())).append("]")
                    .append("更新为")
                    .append("[").append(formatBigDecimalTime(newOverTimeConfig.getRestEffectiveTime())).append("]").append("\n");
        }
        if (!StringUtils.equals(newOverTimeConfig.getRestSubsidyType(), currentConfig.getRestSubsidyType())) {
            sb.append("休息日加班补贴方式:")
                    .append("[").append(currentConfig.getRestSubsidyType()).append("]")
                    .append("更新为")
                    .append("[").append(newOverTimeConfig.getRestSubsidyType()).append("]").append("\n");
        }
        if (Objects.nonNull(newOverTimeConfig.getHolidayEffectiveTime()) &&
                newOverTimeConfig.getHolidayEffectiveTime().compareTo(currentConfig.getHolidayEffectiveTime()) != 0) {
            sb.append("节假日最长有效加班时间:")
                    .append("[").append(formatBigDecimalTime(currentConfig.getHolidayEffectiveTime())).append("]")
                    .append("更新为")
                    .append("[").append(formatBigDecimalTime(newOverTimeConfig.getHolidayEffectiveTime())).append("]").append("\n");
        }
        if (!StringUtils.equals(newOverTimeConfig.getHolidaySubsidyType(), currentConfig.getHolidaySubsidyType())) {
            sb.append("节假日加班补贴方式:")
                    .append("[").append(currentConfig.getHolidaySubsidyType()).append("]")
                    .append("更新为")
                    .append("[").append(newOverTimeConfig.getHolidaySubsidyType()).append("]").append("\n");
        }

        // 处理部门适用范围变更日志
        Set<Long> newDeptSet = new HashSet<>(newOverTimeConfig.listDeptIds());
        Set<Long> currentDeptSet = new HashSet<>(currentConfig.listDeptIds());

        // 计算部门差异集
        // 新增的部门 = 新集合 - 旧集合
        Set<Long> addedDeptSet = new HashSet<>(newDeptSet);
        addedDeptSet.removeAll(currentDeptSet);
        // 移除的部门 = 旧集合 - 新集合
        Set<Long> removedDeptSet = new HashSet<>(currentDeptSet);
        removedDeptSet.removeAll(newDeptSet);

        // 如果有任何变化，则记录日志
        if (!addedDeptSet.isEmpty() || !removedDeptSet.isEmpty()) {
            // 获取所有相关部门信息（新旧部门的并集）
            Map<Long, AttendanceDept> deptMap = deptService.listByDeptIds(Sets.union(newDeptSet, currentDeptSet))
                    .stream()
                    .collect(Collectors.toMap(AttendanceDept::getId, Function.identity(), (a, b) -> a)); // 如果有重复键，保留第一个

            // 将部门ID转换为部门名称字符串
            // 如果集合为空，则使用特殊标记；否则将每个部门ID转换为部门名称，并用逗号连接
            String currentDeptInfo = currentDeptSet.isEmpty() ? NOT_HAVE_RANGE_FLAG : currentDeptSet.stream()
                    .map(deptId -> formatDeptInfo(deptId, deptMap))
                    .collect(Collectors.joining(","));
            String newDeptInfo = newDeptSet.isEmpty() ? NOT_HAVE_RANGE_FLAG : newDeptSet.stream()
                    .map(deptId -> formatDeptInfo(deptId, deptMap))
                    .collect(Collectors.joining(","));

            sb.append("适用部门:")
                    .append("[").append(currentDeptInfo).append("]")
                    .append("更新为")
                    .append("[").append(newDeptInfo).append("]").append("\n");

            // 添加更详细的变更信息
            if (!addedDeptSet.isEmpty()) {
                String addedDeptInfo = addedDeptSet.stream()
                        .map(deptId -> formatDeptInfo(deptId, deptMap))
                        .collect(Collectors.joining(","));
                sb.append("  - 新增:[").append(addedDeptInfo).append("]\n");
            }
            if (!removedDeptSet.isEmpty()) {
                String removedDeptInfo = removedDeptSet.stream()
                        .map(deptId -> formatDeptInfo(deptId, deptMap))
                        .collect(Collectors.joining(","));
                sb.append("  - 移除:[").append(removedDeptInfo).append("]\n");
            }
        }

        // 处理人员适用范围变更日志
        Set<Long> newUserSet = overTimeConfigRangeDao.listActivedConfigByConfigId(newOverTimeConfig.getId())
                .stream()
                .filter(OverTimeConfigRangeDO::areUserRange)
                .map(OverTimeConfigRangeDO::getBizId)
                .collect(Collectors.toSet());
        Set<Long> currentUserSet = overTimeConfigRangeDao.listActivedConfigByConfigId(currentConfig.getId())
                .stream()
                .filter(OverTimeConfigRangeDO::areUserRange)
                .map(OverTimeConfigRangeDO::getBizId)
                .collect(Collectors.toSet());

        // 计算人员差异集
        // 新增的人员 = 新集合 - 旧集合
        Set<Long> added = new HashSet<>(newUserSet);
        added.removeAll(currentUserSet);
        // 移除的人员 = 旧集合 - 新集合
        Set<Long> removed = new HashSet<>(currentUserSet);
        removed.removeAll(newUserSet);

        // 如果有任何变化，则记录日志
        if (!added.isEmpty() || !removed.isEmpty()) {
            // 获取所有相关用户信息（新旧用户的并集）
            Map<Long, AttendanceUser> userMap = userService.listUsersByIds(Sets.union(currentUserSet, newUserSet))
                    .stream()
                    .collect(Collectors.toMap(AttendanceUser::getId, Function.identity(), (a, b) -> a)); // 如果有重复键，保留第一个

            // 将用户ID转换为用户信息字符串
            // 如果集合为空，则使用特殊标记；否则将每个用户ID转换为"用户名称(用户编码)"格式，并用逗号连接
            String currentUserInfo = currentUserSet.isEmpty() ? NOT_HAVE_RANGE_FLAG : currentUserSet.stream()
                    .map(userId -> formatUserInfo(userId, userMap))
                    .collect(Collectors.joining(","));
            String newUserInfo = newUserSet.isEmpty() ? NOT_HAVE_RANGE_FLAG : newUserSet.stream()
                    .map(userId -> formatUserInfo(userId, userMap))
                    .collect(Collectors.joining(","));

            sb.append("适用员工:")
                    .append("[").append(currentUserInfo).append("]")
                    .append("更新为")
                    .append("[").append(newUserInfo).append("]").append("\n");

            // 添加更详细的变更信息
            if (!added.isEmpty()) {
                String addedUserInfo = added.stream()
                        .map(userId -> formatUserInfo(userId, userMap))
                        .collect(Collectors.joining(","));
                sb.append("  - 新增:[").append(addedUserInfo).append("]\n");
            }
            if (!removed.isEmpty()) {
                String removedUserInfo = removed.stream()
                        .map(userId -> formatUserInfo(userId, userMap))
                        .collect(Collectors.joining(","));
                sb.append("  - 移除:[").append(removedUserInfo).append("]\n");
            }
        }

        return sb.toString();
    }

    // 测试方法已移除
}
