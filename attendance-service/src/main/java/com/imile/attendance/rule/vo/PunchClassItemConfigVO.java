package com.imile.attendance.rule.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
public class PunchClassItemConfigVO implements Serializable {

    private Long id;

    /**
     * 班次规则ID
     */
    private Long punchClassId;

    /**
     * 序号
     */
    private Integer sortNo;

    /**
     * 上班时间 仅有时分秒信息，形如：09:00:00
     */
    private String punchInTime;

    /**
     * 下班时间 仅有时分秒信息，形如：18:00:00
     */
    private String punchOutTime;

    /**
     * 最早上班打卡时间 仅有时分秒信息，形如： 09:00:00
     */
    private String earliestPunchInTime;

    /**
     * 最晚上班打卡时间 仅有时分秒信息
     */
    private String latestPunchInTime;

    /**
     * 最晚下班打卡时间 仅有时分秒信息，形如：19:00:00
     */
    private String latestPunchOutTime;

    /**
     * 是否跨天 跨天是指打卡时间是第二天：0：不跨天，1：跨天
     */
    private Integer isAcross;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 弹性时间
     */
    private BigDecimal elasticTime;

    /**
     * 法定工作时长（不包含休息时间）
     */
    private BigDecimal legalWorkingHours;

    /**
     * 出勤时长（包含休息时间）
     */
    private BigDecimal attendanceHours;

    /**
     * 休息开始时间 仅有时分秒信息，形如：09:00:00
     */
    private String restStartTime;

    /**
     * 休息结束时间 仅有时分秒信息，形如：09:00:00
     */
    private String restEndTime;
}
