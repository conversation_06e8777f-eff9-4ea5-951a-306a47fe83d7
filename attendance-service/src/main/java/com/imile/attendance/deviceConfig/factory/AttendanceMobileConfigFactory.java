package com.imile.attendance.deviceConfig.factory;

import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.deviceConfig.command.AttendanceMobileConfigAddCommand;
import com.imile.attendance.deviceConfig.command.AttendanceMobileConfigDeleteCommand;
import com.imile.attendance.deviceConfig.mapstruct.AttendanceMobileConfigMapstruct;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.repository.deviceConfig.dao.AttendanceMobileConfigDao;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceMobileConfigDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/4/18
 * @Description
 */
@Slf4j
@Service
public class AttendanceMobileConfigFactory {

    @Resource
    private AttendanceMobileConfigDao attendanceMobileConfigDao;

    /**
     * 考勤手机：新增数据
     */
    public void add(AttendanceMobileConfigAddCommand addCommand) {
        // mobile add check
        checkAddParam(addCommand);
        // save
        AttendanceMobileConfigDO mobileConfigDO = AttendanceMobileConfigMapstruct.INSTANCE.toAddAttendanceMobileConfigDO(addCommand);
        attendanceMobileConfigDao.save(mobileConfigDO);
    }

    /**
     * 考勤手机解绑
     */
    public void delete(AttendanceMobileConfigDeleteCommand deleteCommand) {
        // mobile delete check
        Long mobileConfigId = deleteCommand.getMobileConfigId();
        AttendanceMobileConfigDO localMobileConfig = attendanceMobileConfigDao.getById(mobileConfigId);
        if (Objects.isNull(localMobileConfig)) {
            log.info("考勤手机表主键id：{}，未查询到对应的考勤手机信息", deleteCommand.getMobileConfigId());
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        // delete
        AttendanceMobileConfigDO deleteDO = new AttendanceMobileConfigDO();
        deleteDO.setId(localMobileConfig.getId());
        deleteDO.setIsDelete(IsDeleteEnum.YES.getCode());
        BaseDOUtil.fillDOUpdate(deleteDO);
        attendanceMobileConfigDao.updateById(deleteDO);
    }

    private void checkAddParam(AttendanceMobileConfigAddCommand addCommand) {
        log.info("保存考勤手机，参数：{}", addCommand);
        if (ObjectUtil.isNull(addCommand)) {
            log.info("保存考勤手机，参数为null，无法保存");
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
        }
    }
}
