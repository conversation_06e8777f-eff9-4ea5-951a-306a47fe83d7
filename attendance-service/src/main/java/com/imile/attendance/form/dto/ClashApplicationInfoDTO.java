package com.imile.attendance.form.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-21
 * @version: 1.0
 */
@Data
public class ClashApplicationInfoDTO {

    /**
     * 冲突单据
     */
    private String applicationCode;

    /**
     * 冲突单据ID
     */
    private Long applicationFormId;

    /**
     * 审批单号ID(审批中心的ID)
     */
    private Long approvalId;

    /**
     * 冲突单据类型
     */
    private String formType;

    /**
     * 冲突单据状态
     */
    private String formStatus;

    /**
     * 假期配置主键(可能为空，如果是外勤就为空)
     */
    private Long configId;

    /**
     * 假期名称(可能为空，如果是外勤就为空)
     */
    private String leaveName;

    /**
     * 假期简称
     */
    private String leaveShortName;

    /**
     * 请假单位
     */
    private String leaveUnit;

    /**
     * 请假/外勤开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    /**
     * 请假/外勤结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    /**
     * 冲突单据的预计外勤/请假时长
     */
    private String expectedTime;
}
