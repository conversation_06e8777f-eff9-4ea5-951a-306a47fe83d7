package com.imile.attendance.form;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.ApplicationFormTypeEnum;
import com.imile.attendance.enums.form.ApplicationRelationTypeEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.form.dto.ApprovalCreateRoleUserDTO;
import com.imile.attendance.form.dto.ApprovalCreateUserDTO;
import com.imile.attendance.form.dto.ApprovalDetailStepRecordDTO;
import com.imile.attendance.form.dto.ApprovalPreviewErrorUserDTO;
import com.imile.attendance.form.dto.ApprovalPreviewSuccessUserDTO;
import com.imile.attendance.form.dto.ApprovalUserInfoDTO;
import com.imile.attendance.form.dto.ClashApplicationInfoDTO;
import com.imile.attendance.form.dto.DayDurationInfoDTO;
import com.imile.attendance.form.param.LeaveAddParam;
import com.imile.attendance.form.param.OutOfOfficeAddParam;
import com.imile.attendance.form.param.ReissueCardAddParam;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveDetailQuery;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormAttrDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormRelationDao;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.infrastructure.repository.form.query.ApplicationFormQuery;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateFormatUtils;
import com.imile.attendance.vacation.UserLeaveDetailService;
import com.imile.attendance.vacation.UserLeaveStageDetailService;
import com.imile.bpm.enums.ApprovalRoleEnum;
import com.imile.bpm.enums.ApprovalRoleValueEnum;
import com.imile.bpm.mq.dto.ApprovalCreateRoleUserApiDTO;
import com.imile.bpm.mq.dto.ApprovalCreateUserApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiDTO;
import com.imile.bpm.mq.dto.ApprovalTypeFieldApiDTO;
import com.imile.bpm.mq.dto.ApprovalUserInfoApiDTO;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.idwork.IdWorkerUtil;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.imile.attendance.util.BaseDOUtil.fillDOInsert;

/**
 * <AUTHOR> chen
 * @Date 2025/3/20
 * @Description 通用审批表单操作服务
 */
@Service
public class CommonFormOperationService {

    @Resource
    private AttendanceFormManage formManage;
    @Resource
    private AttendanceFormAttrDao formAttrDao;
    @Resource
    private AttendanceFormRelationDao formRelationDao;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private CountryService countryService;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private UserLeaveDetailService userLeaveDetailService;
    @Resource
    private UserLeaveStageDetailService userLeaveStageDetailService;
    @Resource
    private DefaultIdWorker defaultIdWorker;

    @Value("#{${country.main.poc.map:{UAE:'2102255',OMN:'2102255',KSA:'2102474',CHN:'2101947',MEX:'2104959',TUR:'2102354',BRA:'2105554',KWT:'2103221',QAT:'2103221',JOR:'2103221',BHR:'2103221',LBN:'2103221',HQ:'2104453',POL:'2104453',DEU:'2104453',FRA:'2104453',GBR:'2104453',CHL:'2104453',RSA:'2104453',NLD:'2104453',AUS:'2104453',ITA:'2104453'}}}")
    private Map<String, String> countryMainPocMap;


    /**
     * 构建预览实体
     */
    public void previewDTOBuildContainsErrors(List<ApprovalEmptyRecordApiDTO> recordApiDTOList,
                                              List<ApprovalDetailStepRecordDTO> resultDTOList,
                                              String userCode) {
        //获取所有国家的HR信息
        List<String> hrUserCodeList = new ArrayList<>();
        countryMainPocMap.forEach((k, v) -> hrUserCodeList.add(v));
        List<AttendanceUser> attendanceUsers = userService.listByUserCodes(hrUserCodeList);
        Map<String, List<AttendanceUser>> hrUserCodeMap = attendanceUsers.stream()
                .collect(Collectors.groupingBy(AttendanceUser::getUserCode));

        //获取所有国家信息
        List<CountryConfigDTO> countryConfigDTOList = countryService.queryAllCountryConfigList();
        List<CountryDTO> countryDTOList = BeanUtils.convert(CountryDTO.class, countryConfigDTOList);
        Map<String, List<CountryDTO>> countryMap = countryDTOList.stream()
                .collect(Collectors.groupingBy(CountryDTO::getCountryName));

        //获取所有人员(角色和指定人员总和)
        List<String> allAppointUserCodeList = new ArrayList<>();
        List<String> allRoleUserCodeList = new ArrayList<>();
        List<String> allUserCodeList = new ArrayList<>();
        for (ApprovalEmptyRecordApiDTO approvalEmptyRecordApiDTO : recordApiDTOList) {
            ApprovalCreateUserApiDTO approvalCreateUserApiDTO = approvalEmptyRecordApiDTO.getApprovalCreateUserApiDTO();
            if (approvalCreateUserApiDTO == null) {
                approvalCreateUserApiDTO = new ApprovalCreateUserApiDTO();
            }
            //所有指定人员
            List<ApprovalUserInfoApiDTO> appointUserList = approvalCreateUserApiDTO.getApprovalUserInfos();
            if (CollectionUtils.isEmpty(appointUserList)) {
                appointUserList = new ArrayList<>();
            }
            List<String> appointUserCodeList = appointUserList.stream()
                    .map(ApprovalUserInfoApiDTO::getUserCode)
                    .collect(Collectors.toList());
            allAppointUserCodeList.addAll(appointUserCodeList);

            //所有角色对应人员
            List<ApprovalCreateRoleUserApiDTO> approvalCreateRoleUserList =
                    approvalCreateUserApiDTO.getApprovalCreateRoleUserApiDTOS();
            if (CollectionUtils.isEmpty(approvalCreateRoleUserList)) {
                approvalCreateRoleUserList = new ArrayList<>();
            }
            for (ApprovalCreateRoleUserApiDTO roleUserDTO : approvalCreateRoleUserList) {
                List<ApprovalUserInfoApiDTO> roleUserInfos = roleUserDTO.getApprovalUserInfos();
                if (CollectionUtils.isEmpty(roleUserInfos)) {
                    continue;
                }
                List<String> roleUserCodeList = roleUserInfos.stream()
                        .map(ApprovalUserInfoApiDTO::getUserCode)
                        .collect(Collectors.toList());
                allRoleUserCodeList.addAll(roleUserCodeList);
            }
        }
        allUserCodeList.addAll(allAppointUserCodeList);
        allUserCodeList.addAll(allRoleUserCodeList);
        allUserCodeList.add(userCode);
        //调用HR接口，获取所有人员信息
        List<AttendanceUser> users = userService.listByUserCodes(allUserCodeList);
        Map<String, List<AttendanceUser>> userCodeMap = users.stream()
                .collect(Collectors.groupingBy(AttendanceUser::getUserCode));


        int temp = 1;
        for (ApprovalEmptyRecordApiDTO approvalEmptyRecordApiDTO : recordApiDTOList) {
            //发起人节点
            if (temp == 1) {
                ApprovalDetailStepRecordDTO createApprovalUserDTO = new ApprovalDetailStepRecordDTO();
                createApprovalUserDTO.setApprovalRecordType("approval");
                createApprovalUserDTO.setRecordStatus(-1);
                createApprovalUserDTO.setRecordStatusName(RequestInfoHolder.isChinese() ? "发起审批" : "Initiate approval");
                createApprovalUserDTO.setStepName(RequestInfoHolder.isChinese() ? "发起审批" : "Initiate approval");
                createApprovalUserDTO.setRecordStatusUpdateDate(new Date());
                createApprovalUserDTO.setStepId("APPLY");
                List<ApprovalUserInfoDTO> approvalUserDTOList =
                        BeanUtils.convert(ApprovalUserInfoDTO.class, approvalEmptyRecordApiDTO.getApprovalUserInfoDTOS());
                createApprovalUserDTO.setApprovalUserInfoDTOS(approvalUserDTOList);
                resultDTOList.add(createApprovalUserDTO);
                temp++;
                continue;
            }
            //非发起人节点
            ApprovalDetailStepRecordDTO stepRecordDTO =
                    BeanUtils.convert(approvalEmptyRecordApiDTO, ApprovalDetailStepRecordDTO.class);
            // 这边使用bpm返回的ApprovalRecordType，主要是为了区分抄送，前端可以把抄送过滤掉
            //stepRecordDTO.setApprovalRecordType("approval");
            stepRecordDTO.setRecordStatus(1);
            stepRecordDTO.setRecordStatusName(RequestInfoHolder.isChinese() ? "审批中" : "In review");
            //指定人员
            List<ApprovalUserInfoDTO> approvalUserDTOList =
                    BeanUtils.convert(ApprovalUserInfoDTO.class, approvalEmptyRecordApiDTO.getApprovalUserInfoDTOS());
            approvalUserDTOList.forEach(item -> {
                item.setApprovalOperation("APPROVING");
            });
            stepRecordDTO.setApprovalUserInfoDTOS(approvalUserDTOList);

            //角色/指定人员处理
            //整体流程主节点信息
            List<ApprovalPreviewErrorUserDTO> approvalPreviewErrorUserDTOList = new ArrayList<>();
            List<ApprovalPreviewSuccessUserDTO> approvalPreviewSuccessUserDTOList = new ArrayList<>();
            //所有查询到的用户
            List<ApprovalUserInfoDTO> findUserList = stepRecordDTO.getApprovalUserInfoDTOS();

            ApprovalCreateUserApiDTO approvalCreateUserApiDTO = approvalEmptyRecordApiDTO.getApprovalCreateUserApiDTO();
            if (approvalCreateUserApiDTO == null) {
                approvalCreateUserApiDTO = new ApprovalCreateUserApiDTO();
            }

            ApprovalCreateUserDTO approvalCreateUserDTO = new ApprovalCreateUserDTO();
            stepRecordDTO.setApprovalCreateUserDTO(approvalCreateUserDTO);
            List<ApprovalUserInfoApiDTO> approvalUserInfoApiDTOS = approvalCreateUserApiDTO.getApprovalUserInfos();
            if (CollectionUtils.isEmpty(approvalUserInfoApiDTOS)) {
                approvalUserInfoApiDTOS = new ArrayList<>();
            }
            approvalCreateUserDTO.setApprovalUserInfos(
                    BeanUtils.convert(ApprovalUserInfoDTO.class, approvalUserInfoApiDTOS));

            List<ApprovalCreateRoleUserDTO> approvalCreateRoleUserDTOS = new ArrayList<>();
            approvalCreateUserDTO.setApprovalCreateRoleUserDTOS(approvalCreateRoleUserDTOS);
            List<ApprovalCreateRoleUserApiDTO> approvalCreateRoleUserApiDTOS =
                    approvalCreateUserApiDTO.getApprovalCreateRoleUserApiDTOS();
            if (CollectionUtils.isEmpty(approvalCreateRoleUserApiDTOS)) {
                approvalCreateRoleUserApiDTOS = new ArrayList<>();
            }
            for (ApprovalCreateRoleUserApiDTO roleUserApiDTO : approvalCreateRoleUserApiDTOS) {
                ApprovalCreateRoleUserDTO roleUserDTO = new ApprovalCreateRoleUserDTO();
                roleUserDTO.setApprovalRole(roleUserApiDTO.getApprovalRole());
                roleUserDTO.setApprovalRoleName(roleUserApiDTO.getApprovalRoleName());
                roleUserDTO.setFetchObject(roleUserApiDTO.getFetchObject());
                roleUserDTO.setFetchObjectName(roleUserApiDTO.getFetchObjectName());
                roleUserDTO.setFetchObjectValue(roleUserApiDTO.getFetchObjectValue());
                roleUserDTO.setFetchObjectValueName(roleUserApiDTO.getFetchObjectValueName());
                roleUserDTO.setApprovalUserCodes(roleUserApiDTO.getApprovalUserCodes());
                if (CollectionUtils.isNotEmpty(roleUserApiDTO.getApprovalUserInfos())) {
                    roleUserDTO.setApprovalUserInfos(
                            BeanUtils.convert(ApprovalUserInfoDTO.class, roleUserApiDTO.getApprovalUserInfos()));
                }
                approvalCreateRoleUserDTOS.add(roleUserDTO);
            }

            previewUserInfoFind(userCode, countryMap, hrUserCodeMap, userCodeMap, findUserList, approvalCreateUserDTO, approvalPreviewErrorUserDTOList, approvalPreviewSuccessUserDTOList);

            approvalCreateUserDTO.setApprovalPreviewErrorUserDTOList(approvalPreviewErrorUserDTOList);
            approvalCreateUserDTO.setApprovalPreviewSuccessUserDTOList(approvalPreviewSuccessUserDTOList);
            resultDTOList.add(stepRecordDTO);
            temp++;
        }
    }

    /**
     * 删除（不包含加班）
     */
    public void delete(Long formId) {
        //查询当前单据是否存在
        AttendanceFormDetailBO formDetailBO = formManage.getFormDetailById(formId);
        AttendanceFormDO formDO = formDetailBO.getFormDO();
        if (formDO == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        //只有暂存状态的单据可以被删除
        if (!StringUtils.equalsIgnoreCase(FormStatusEnum.STAGING.getCode(), formDO.getFormStatus())) {
            throw BusinessException.get(ErrorCodeEnum.ONLY_STAGE_STATUS_CAN_BE_DELETE.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.ONLY_STAGE_STATUS_CAN_BE_DELETE.getDesc()));
        }
        formDO.setIsDelete(BusinessConstant.Y);
        BaseDOUtil.fillDOUpdate(formDO);
        List<AttendanceFormRelationDO> relationDOS = formDetailBO.getRelationDOList();
        relationDOS.forEach(item -> {
            item.setIsDelete(BusinessConstant.Y);
            BaseDOUtil.fillDOUpdate(item);
        });
        List<AttendanceFormAttrDO> attrDOS = formDetailBO.getAttrDOList();
        attrDOS.forEach(item -> {
            item.setIsDelete(BusinessConstant.Y);
            BaseDOUtil.fillDOUpdate(item);
        });
        formManage.delete(formDO, relationDOS, attrDOS);
    }


    public AttendanceFormAttrDO insertAttrDOBuild(Long formId, String attrKey, String attrValue) {
        AttendanceFormAttrDO formAttrDO = new AttendanceFormAttrDO();
        formAttrDO.setId(IdWorkerUtil.getId());
        formAttrDO.setFormId(formId);
        formAttrDO.setAttrKey(attrKey);
        formAttrDO.setAttrValue(attrValue);
        fillDOInsert(formAttrDO);
        return formAttrDO;
    }

    public void customFieldBuild(List<ApprovalTypeFieldApiDTO> fieldApiDTOList,
                                 String fieldType, String fieldValue,
                                 Map<String, String> fieldValueMap) {
        ApprovalTypeFieldApiDTO fieldApiDTO = new ApprovalTypeFieldApiDTO();
        fieldApiDTO.setFieldType(fieldType);
        fieldApiDTO.setFieldValue(fieldValue);
        fieldApiDTO.setFieldValueMap(fieldValueMap);
        fieldApiDTOList.add(fieldApiDTO);
    }

    public void repeatRevokeCheck(Long formId) {
        List<Long> revokeFormIdList = formRelationDao.selectRelationByRelationIdList(Collections.singletonList(formId))
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.APPLICATION_FORM.getCode()))
                .map(AttendanceFormRelationDO::getFormId)
                .collect(Collectors.toList());
        List<AttendanceFormDO> revokeFormList = formManage.selectByIdList(revokeFormIdList)
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getFormStatus(), FormStatusEnum.IN_REVIEW.getCode()) ||
                        StringUtils.equalsIgnoreCase(item.getFormStatus(), FormStatusEnum.PASS.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(revokeFormList)) {
            throw BusinessException.get(ErrorCodeEnum.NOT_REVOKE_REPEAT.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.NOT_REVOKE_REPEAT.getDesc()));
        }
    }

    /**
     * 查找冲突单据
     */
    public void selectClashApplication(Long userId, Date startDate, Date endDate,
                                       List<ClashApplicationInfoDTO> clashApplicationInfoDTOList) {
        ApplicationFormQuery formQuery = new ApplicationFormQuery();
        formQuery.setUserId(userId);
        formQuery.setFromTypeList(Arrays.asList(ApplicationFormTypeEnum.LEAVE.getCode(), ApplicationFormTypeEnum.OUT_OF_OFFICE.getCode()));
        formQuery.setStatusList(Arrays.asList(FormStatusEnum.IN_REVIEW.getCode(), FormStatusEnum.PASS.getCode()));
        List<AttendanceFormDO> formDOList = formManage.selectForm(formQuery);
        List<Long> formIdList = formDOList.stream().map(AttendanceFormDO::getId).collect(Collectors.toList());
        //查找属性表
        List<AttendanceFormAttrDO> formAttrDOList = formAttrDao.selectFormAttrByFormIdLit(formIdList);
        Map<Long, List<AttendanceFormAttrDO>> formIdMap = formAttrDOList.stream()
                .collect(Collectors.groupingBy(AttendanceFormAttrDO::getFormId));
        for (AttendanceFormDO formDO : formDOList) {
            List<AttendanceFormAttrDO> existFormList = formIdMap.get(formDO.getId());
            if (CollectionUtils.isEmpty(existFormList)) {
                continue;
            }
            Map<String, AttendanceFormAttrDO> attrMap = existFormList.stream()
                    .collect(Collectors.toMap(AttendanceFormAttrDO::getAttrKey, o -> o, (v1, v2) -> v1));
            AttendanceFormAttrDO dayInfoDO = attrMap.get(ApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode());
            String descCN = null;
            String descEN = null;
            if (dayInfoDO != null) {
                List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayInfoDO.getAttrValue(), DayDurationInfoDTO.class);
                BigDecimal days = BigDecimal.ZERO;
                BigDecimal hours = BigDecimal.ZERO;
                BigDecimal minutes = BigDecimal.ZERO;
                for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                    days = days.add(dayDurationInfoDTO.getDays());
                    hours = hours.add(dayDurationInfoDTO.getHours());
                    minutes = minutes.add(dayDurationInfoDTO.getMinutes());
                }
                descCN = days + "天" + hours + "小时" + minutes + "分钟";
                descEN = days + "days" + hours + "hours" + minutes + "minutes";
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), ApplicationFormTypeEnum.LEAVE.getCode())) {
                AttendanceFormAttrDO configIdDO = attrMap.get(ApplicationFormAttrKeyEnum.configID.getLowerCode());
                AttendanceFormAttrDO leaveNameDO = attrMap.get(ApplicationFormAttrKeyEnum.leaveType.getLowerCode());
                AttendanceFormAttrDO leaveUnitDO = attrMap.get(ApplicationFormAttrKeyEnum.leaveUnit.getLowerCode());
                AttendanceFormAttrDO leaveStartDateDO = attrMap.get(ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode());
                AttendanceFormAttrDO leaveEndDateDO = attrMap.get(ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode());

                if (leaveStartDateDO == null || leaveEndDateDO == null || leaveNameDO == null || leaveUnitDO == null) {
                    continue;
                }
                Date leaveStartDate = DateFormatUtils.parseDateTime(leaveStartDateDO.getAttrValue());
                Date leaveEndDate = DateFormatUtils.parseDateTime(leaveEndDateDO.getAttrValue());
                if (startDate.compareTo(leaveEndDate) > -1 || endDate.compareTo(leaveStartDate) < 1) {
                    //没有交集，没有冲突
                    continue;
                }
                ClashApplicationInfoDTO clashApplicationInfoDTO = new ClashApplicationInfoDTO();
                clashApplicationInfoDTO.setApplicationCode(formDO.getApplicationCode());
                clashApplicationInfoDTO.setApplicationFormId(formDO.getId());
                clashApplicationInfoDTO.setApprovalId(formDO.getApprovalId());
                clashApplicationInfoDTO.setFormType(formDO.getFormType());
                clashApplicationInfoDTO.setFormStatus(formDO.getFormStatus());
                clashApplicationInfoDTO.setConfigId(Objects.isNull(configIdDO) ? null : Long.parseLong(configIdDO.getAttrValue()));
                clashApplicationInfoDTO.setLeaveName(leaveNameDO.getAttrValue());
                clashApplicationInfoDTO.setLeaveUnit(leaveUnitDO.getAttrValue());
                clashApplicationInfoDTO.setStartDate(leaveStartDate);
                clashApplicationInfoDTO.setEndDate(leaveEndDate);
                clashApplicationInfoDTO.setExpectedTime(RequestInfoHolder.isChinese() ? descCN : descEN);
                clashApplicationInfoDTOList.add(clashApplicationInfoDTO);
                continue;
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), ApplicationFormTypeEnum.OUT_OF_OFFICE.getCode())) {
                AttendanceFormAttrDO outOfOfficeStartDateDO = attrMap.get(ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode());
                AttendanceFormAttrDO outOfOfficeEndDateDO = attrMap.get(ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode());
                if (outOfOfficeStartDateDO == null || outOfOfficeEndDateDO == null) {
                    continue;
                }
                Date outOfOfficeStartDate = DateFormatUtils.parseDateTime(outOfOfficeStartDateDO.getAttrValue());
                Date outOfOfficeEndDate = DateFormatUtils.parseDateTime(outOfOfficeEndDateDO.getAttrValue());
                if (startDate.compareTo(outOfOfficeEndDate) > -1 || endDate.compareTo(outOfOfficeStartDate) < 1) {
                    //没有交集，没有冲突
                    continue;
                }
                ClashApplicationInfoDTO clashApplicationInfoDTO = new ClashApplicationInfoDTO();
                clashApplicationInfoDTO.setApplicationCode(formDO.getApplicationCode());
                clashApplicationInfoDTO.setApplicationFormId(formDO.getId());
                clashApplicationInfoDTO.setApprovalId(formDO.getApprovalId());
                clashApplicationInfoDTO.setFormType(formDO.getFormType());
                clashApplicationInfoDTO.setFormStatus(formDO.getFormStatus());
                clashApplicationInfoDTO.setStartDate(outOfOfficeStartDate);
                clashApplicationInfoDTO.setEndDate(outOfOfficeEndDate);
                clashApplicationInfoDTO.setExpectedTime(RequestInfoHolder.isChinese() ? descCN : descEN);
                clashApplicationInfoDTOList.add(clashApplicationInfoDTO);
            }
        }
    }


    /**
     * 预览审批人员信息查询
     *
     * @param applyUserCode                     申请人
     * @param countryMap                        国家信息
     * @param hrUserCodeMap                     HR信息
     * @param userCodeMap                       用户信息
     * @param approvalUserInfoDTOS              所有审批人员
     * @param approvalCreateUserDTO             流程信息
     * @param approvalPreviewErrorUserDTOList   错误信息
     * @param approvalPreviewSuccessUserDTOList 正确信息
     */
    private void previewUserInfoFind(String applyUserCode,
                                     Map<String, List<CountryDTO>> countryMap,
                                     Map<String, List<AttendanceUser>> hrUserCodeMap,
                                     Map<String, List<AttendanceUser>> userCodeMap,
                                     List<ApprovalUserInfoDTO> approvalUserInfoDTOS,
                                     ApprovalCreateUserDTO approvalCreateUserDTO,
                                     List<ApprovalPreviewErrorUserDTO> approvalPreviewErrorUserDTOList,
                                     List<ApprovalPreviewSuccessUserDTO> approvalPreviewSuccessUserDTOList) {
        List<String> findUserCodeList = approvalUserInfoDTOS.stream().map(item -> item.getUserCode()).collect(Collectors.toList());
        //所有指定人员
        List<ApprovalUserInfoDTO> appointUserList = approvalCreateUserDTO.getApprovalUserInfos();
        if (CollectionUtils.isEmpty(appointUserList)) {
            appointUserList = new ArrayList<>();
        }
        for (ApprovalUserInfoDTO userInfoDTO : appointUserList) {
            List<AttendanceUser> userList = userCodeMap.get(userInfoDTO.getUserCode());
            if (CollectionUtils.isEmpty(userList)) {
                continue;
            }
            //获取HR信息
            String hrUserCode = countryMainPocMap.get(userList.get(0).getOriginCountry());
            String userHrMessage = hrUserCode;
            if (StringUtils.isNotBlank(hrUserCode)) {
                List<AttendanceUser> hrUserDetailList = hrUserCodeMap.get(hrUserCode);
                if (CollectionUtils.isNotEmpty(hrUserDetailList)) {
                    userHrMessage = hrUserDetailList.get(0).getUserName();
                }
            }
            if (findUserCodeList.contains(userInfoDTO.getUserCode())) {
                //指定人员不存在
                if (StringUtils.equalsIgnoreCase(userList.get(0).getWorkStatus(), "DIMISSION")) {
                    ApprovalPreviewErrorUserDTO errorUserDTO = new ApprovalPreviewErrorUserDTO();
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? "指定员工  " + userList.get(0).getUserName() + " 已经离职" : "The fixed approver " + userList.get(0).getUserName() + " has resigned");
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    errorUserDTO.setTemp(1);
                    approvalPreviewErrorUserDTOList.add(errorUserDTO);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(userList.get(0).getStatus(), "DISABLED")) {
                    ApprovalPreviewErrorUserDTO errorUserDTO = new ApprovalPreviewErrorUserDTO();
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? "指定员工 " + userList.get(0).getUserName() + " 已经冻结" : "The fixed approver " + userList.get(0).getUserName() + " has been disabled");
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    errorUserDTO.setTemp(1);
                    approvalPreviewErrorUserDTOList.add(errorUserDTO);
                    continue;
                }
                //指定人员存在
                ApprovalPreviewSuccessUserDTO successUserDTO = new ApprovalPreviewSuccessUserDTO();
                successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userList.get(0).getUserName() + " 是指定员工" : userList.get(0).getUserName() + " is fixed in process");
                successUserDTO.setUserHrMessage(userHrMessage);
                successUserDTO.setTemp(1);
                approvalPreviewSuccessUserDTOList.add(successUserDTO);
                //continue;
            }
        }

        //所有角色对应人员
        List<ApprovalCreateRoleUserDTO> approvalCreateRoleUserDTOS = approvalCreateUserDTO.getApprovalCreateRoleUserDTOS();
        if (CollectionUtils.isEmpty(approvalCreateRoleUserDTOS)) {
            approvalCreateRoleUserDTOS = new ArrayList<>();
        }
        //申请人HR相关信息
        //获取HR信息
        List<AttendanceUser> applyUserList = userCodeMap.get(applyUserCode);
        if (CollectionUtils.isEmpty(applyUserList)) {
            return;
        }

        for (ApprovalCreateRoleUserDTO roleUserDTO : approvalCreateRoleUserDTOS) {
            List<ApprovalUserInfoDTO> roleUserInfos = roleUserDTO.getApprovalUserInfos();
            if (CollectionUtils.isEmpty(roleUserInfos)) {
                //这个角色没有找到人
                ApprovalPreviewErrorUserDTO errorUserDTO = new ApprovalPreviewErrorUserDTO();
                errorUserDTO.setApprovalRole(roleUserDTO.getApprovalRole());
                errorUserDTO.setFetchObject(roleUserDTO.getFetchObject());
                errorUserDTO.setFetchObjectValue(roleUserDTO.getFetchObjectValue());
                errorUserDTO.setTemp(2);
                approvalPreviewErrorUserDTOList.add(errorUserDTO);
                ApprovalRoleValueEnum approvalRoleValueEnum = ApprovalRoleValueEnum.getInstanceByCode(roleUserDTO.getFetchObject());
                ApprovalRoleEnum approvalRoleEnum = ApprovalRoleEnum.getInstanceByCode(roleUserDTO.getApprovalRole());
                if (approvalRoleValueEnum == null || approvalRoleEnum == null) {
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.BE_APPROVERED.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.APPLY_USER.getCode())) {
                    String userMessageCn = applyUserList.get(0).getUserName() + " - " + approvalRoleEnum.getDescCN() + " 不存在";
                    // String userMessageEn = applyUserInfoDetailApiDTOList.get(0).getUserName() + " - " + approvalRoleEnum.getDescUS() + " not found";
                    // 由于枚举是bpm定义的，改不了，所以直接先写死汇报上级的英文
                    String userMessageEn = applyUserList.get(0).getUserName() + " - " + "Line Manager" + " not found";
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(applyUserList.get(0).getOriginCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<AttendanceUser> hrUserList = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserList)) {
                            userHrMessage = hrUserList.get(0).getUserName();
                        }
                    }
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPT.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.STATION.getCode())) {

                    List<AttendanceDept> deptApiDTOList = deptService.selectDeptByIds(
                            Collections.singletonList(Long.valueOf(roleUserDTO.getFetchObjectValue())));
                    if (CollectionUtils.isEmpty(deptApiDTOList)) {
                        continue;
                    }
                    List<CountryDTO> countryDTOList = countryMap.get(deptApiDTOList.get(0).getCountry());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameCn() + " - " + approvalRoleEnum.getDescCN() + " 不存在";
                    String userMessageEn = countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameEn() + " - " + approvalRoleEnum.getDescCN() + " not found";
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(deptApiDTOList.get(0).getCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<AttendanceUser> hrUserList = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserList)) {
                            userHrMessage = hrUserList.get(0).getUserName();
                        }
                    }
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.CLEARING_THEME.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPARTURE_COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DESTINATION_COUNTRY.getCode())) {
                    List<CountryDTO> countryDTOList = countryMap.get(roleUserDTO.getFetchObjectValue());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescCN() + "不存在";
                    String userMessageEn = countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescCN() + "not found";
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(countryDTOList.get(0).getCountryName());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<AttendanceUser> hrUserList = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserList)) {
                            userHrMessage = hrUserList.get(0).getUserName();
                        }
                    }
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                continue;
            }

            for (ApprovalUserInfoDTO userInfoDTO : roleUserInfos) {
                List<AttendanceUser> userList = userCodeMap.get(userInfoDTO.getUserCode());
                if (CollectionUtils.isEmpty(userList)) {
                    continue;
                }
                //这个角色找到人
                ApprovalPreviewSuccessUserDTO successUserDTO = new ApprovalPreviewSuccessUserDTO();
                successUserDTO.setApprovalRole(roleUserDTO.getApprovalRole());
                successUserDTO.setFetchObject(roleUserDTO.getFetchObject());
                successUserDTO.setFetchObjectValue(roleUserDTO.getFetchObjectValue());
                successUserDTO.setUserCode(userList.get(0).getUserCode());
                successUserDTO.setTemp(2);
                approvalPreviewSuccessUserDTOList.add(successUserDTO);
                ApprovalRoleValueEnum approvalRoleValueEnum = ApprovalRoleValueEnum.getInstanceByCode(roleUserDTO.getFetchObject());
                ApprovalRoleEnum approvalRoleEnum = ApprovalRoleEnum.getInstanceByCode(roleUserDTO.getApprovalRole());
                if (approvalRoleValueEnum == null || approvalRoleEnum == null) {
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.BE_APPROVERED.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.APPLY_USER.getCode())) {
                    String userMessageCn = userList.get(0).getUserName() + " is " + approvalRoleEnum.getDescCN();
                    String userMessageEn = userList.get(0).getUserName() + " is " + approvalRoleEnum.getDescUS();
                    successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(applyUserList.get(0).getOriginCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<AttendanceUser> hrUserList = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserList)) {
                            userHrMessage = hrUserList.get(0).getUserName();
                        }
                    }
                    successUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPT.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.STATION.getCode())) {
                    List<AttendanceDept> deptList = deptService.selectDeptByIds(Collections.singletonList(Long.valueOf(roleUserDTO.getFetchObjectValue())));
                    if (CollectionUtils.isEmpty(deptList)) {
                        continue;
                    }
                    List<CountryDTO> countryDTOList = countryMap.get(deptList.get(0).getCountry());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = userList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + deptList.get(0).getDeptNameCn() + approvalRoleEnum.getDescCN();
                    String userMessageEn = userList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + deptList.get(0).getDeptNameEn() + approvalRoleEnum.getDescUS();
                    successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(deptList.get(0).getCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<AttendanceUser> hrUserList = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserList)) {
                            userHrMessage = hrUserList.get(0).getUserName();
                        }
                    }
                    successUserDTO.setUserHrMessage(userHrMessage);

                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.CLEARING_THEME.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPARTURE_COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DESTINATION_COUNTRY.getCode())) {
                    List<CountryDTO> countryDTOList = countryMap.get(roleUserDTO.getFetchObjectValue());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = userList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescCN();
                    String userMessageEn = userList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescUS();
                    successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息

                    String hrUserCode = countryMainPocMap.get(countryDTOList.get(0).getCountryName());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<AttendanceUser> hrUserList = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserList)) {
                            userHrMessage = hrUserList.get(0).getUserName();
                        }
                    }
                    successUserDTO.setUserHrMessage(userHrMessage);
                }
            }
        }
    }

    /**
     * 构建表单实体
     * @param leaveAddParam
     * @param outOfOfficeAddParam
     * @param reissueCardAddParam
     */
    public void userBaseInfoBuild(LeaveAddParam leaveAddParam,
                                   OutOfOfficeAddParam outOfOfficeAddParam,
                                   ReissueCardAddParam reissueCardAddParam) {
        Long userId = null;
        if (leaveAddParam != null) {
            userId = leaveAddParam.getUserId();
        }
        if (outOfOfficeAddParam != null) {
            userId = outOfOfficeAddParam.getUserId();
        }
        if (reissueCardAddParam != null) {
            userId = reissueCardAddParam.getUserId();
        }
        if (userId == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        AttendanceUser userInfo = userService.getByUserId(userId);
        if (userInfo == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        if (Objects.nonNull(leaveAddParam)) {
            leaveAddParam.setUserCode(userInfo.getUserCode());
            leaveAddParam.setUserName(userInfo.getUserName());
            leaveAddParam.setDeptId(userInfo.getDeptId());
            leaveAddParam.setPostId(userInfo.getPostId());
            leaveAddParam.setCountry(userInfo.getLocationCountry());
            leaveAddParam.setOriginCountry(userInfo.getOriginCountry());
            leaveAddParam.setIsWarehouseStaff(userInfo.getIsWarehouseStaff());
        }
        if (Objects.nonNull(outOfOfficeAddParam)) {
            outOfOfficeAddParam.setUserCode(userInfo.getUserCode());
            outOfOfficeAddParam.setUserName(userInfo.getUserName());
            outOfOfficeAddParam.setDeptId(userInfo.getDeptId());
            outOfOfficeAddParam.setPostId(userInfo.getPostId());
            outOfOfficeAddParam.setCountry(userInfo.getLocationCountry());
            outOfOfficeAddParam.setOriginCountry(userInfo.getOriginCountry());
            outOfOfficeAddParam.setIsWarehouseStaff(userInfo.getIsWarehouseStaff());
        }
        if (Objects.nonNull(reissueCardAddParam)) {
            reissueCardAddParam.setUserCode(userInfo.getUserCode());
            reissueCardAddParam.setUserName(userInfo.getUserName());
            reissueCardAddParam.setDeptId(userInfo.getDeptId());
            reissueCardAddParam.setPostId(userInfo.getPostId());
            reissueCardAddParam.setCountry(userInfo.getLocationCountry());
            reissueCardAddParam.setOriginCountry(userInfo.getOriginCountry());
            reissueCardAddParam.setIsWarehouseStaff(userInfo.getIsWarehouseStaff());
        }
    }

    /**
     * 外勤/请假-每天时长计算明细
     * 需要获取请假开始时间前一天-请假结束时间后一天的所有排班
     */
    public void dayDurationInfoHandler(Long userId,
                                       Date startDate, Date endDate,
                                       CompanyLeaveConfigDO companyLeaveConfigDO,
                                       List<DayDurationInfoDTO> dayDurationInfoDTOList,
                                       BigDecimal dayAttendanceHours) {
        AttendanceUser userInfo = userService.getByUserId(userId);
        if (userInfo == null || ObjectUtil.isEmpty(userInfo.getUserCode())) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }

        Long beforeDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(startDate, -1), "yyyyMMdd"));
        Long afterDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(endDate, 1), "yyyyMMdd"));
        // 获取打卡记录查询的开始时间和结束时间[这边查询的时间范围是前一天的开始时间和后一天的结束时间，存在跨天情况]
        DateTime punchRecordStartTime = DateUtil.beginOfDay(DateUtil.offsetDay(startDate, -1));
        DateTime punchRecordEndTime = DateUtil.endOfDay(DateUtil.offsetDay(endDate, 1));
        // todo:需要关联打卡规则范围、排班、打卡记录
//        //查询用户的打卡规则范围，看是否当天免打卡
//        List<HrmsAttendancePunchConfigRangeDO> dayPunchConfigRangeDOS = punchConfigManageAdapter.selectUserAllConfigRangeByBizId(Arrays.asList(userId));
//
//        //查询用户的排班记录(前后各多加1天)  可以为空，没有排班
//        List<HrmsAttendanceClassEmployeeConfigDO> classEmployeeConfigDOS = hrmsAttendanceClassEmployeeConfigManage.selectRecordByUserIdList(Arrays.asList(userId), beforeDayId, afterDayId);
//        Map<Long, List<HrmsAttendanceClassEmployeeConfigDO>> employeeDayIdMap = classEmployeeConfigDOS.stream().collect(Collectors.groupingBy(HrmsAttendanceClassEmployeeConfigDO::getDayId));
//        List<Long> employeeConfigIdList = classEmployeeConfigDOS.stream().map(item -> item.getId()).collect(Collectors.toList());
//
//        //生成班次/明细  可能为OFF/PH排班 classId为空
//        List<Long> punchClassIdList = classEmployeeConfigDOS.stream().filter(item -> item.getClassId() != null).map(item -> item.getClassId()).collect(Collectors.toList());
////        List<HrmsAttendancePunchClassConfigDO> classConfigDOS = hrmsAttendancePunchClassConfigManage.selectClassByIdList(punchClassIdList);
//        List<HrmsAttendancePunchClassConfigDO> classConfigDOS = punchConfigManageAdapter.selectClassByIdList(punchClassIdList);
//        Map<Long, List<HrmsAttendancePunchClassConfigDO>> classConfig = classConfigDOS.stream().collect(Collectors.groupingBy(HrmsAttendancePunchClassConfigDO::getId));
//        List<Long> punchIdList = classConfigDOS.stream().map(item -> item.getPunchConfigId()).collect(Collectors.toList());
////        List<HrmsAttendancePunchConfigDO> punchConfigDOList = hrmsAttendancePunchConfigManage.selectAttendancePunchByIdList(punchIdList);
//        List<HrmsAttendancePunchConfigDO> punchConfigDOList = punchConfigManageAdapter.selectAttendancePunchByIdList(punchIdList);
//        Map<Long, List<HrmsAttendancePunchConfigDO>> punchConfigMap = punchConfigDOList.stream().collect(Collectors.groupingBy(HrmsAttendancePunchConfigDO::getId));
////        List<HrmsAttendancePunchClassItemConfigDO> punchClassItemConfigDOList = hrmsAttendancePunchClassItemConfigManage.selectItemConfigByClassId(punchClassIdList);
//        List<HrmsAttendancePunchClassItemConfigDO> punchClassItemConfigDOList = punchConfigManageAdapter.selectItemConfigByClassId(punchClassIdList);
//        Map<Long, List<HrmsAttendancePunchClassItemConfigDO>> punchClassItemConfigMap = punchClassItemConfigDOList.stream().collect(Collectors.groupingBy(HrmsAttendancePunchClassItemConfigDO::getPunchClassId));
//
//        // 查询用户信息
//        // 查询用户的打卡记录
//        EmployeePunchCardRecordQuery query = new EmployeePunchCardRecordQuery();
//        query.setStartTime(punchRecordStartTime);
//        query.setEndTime(punchRecordEndTime);
//        query.setUserCode(userInfoDO.getUserCode());
//        List<EmployeePunchRecordDO> allPunchRecordList = punchRecordDao.listRecords(query);
//        List<UserPunchRecordDTO> userPunchRecordDTOList = new ArrayList<>();
//        for (EmployeePunchRecordDO employeePunchRecordDO : allPunchRecordList) {
//            UserPunchRecordDTO userPunchRecordDTO = new UserPunchRecordDTO();
//            userPunchRecordDTO.setId(employeePunchRecordDO.getId());
//            userPunchRecordDTO.setUserCode(employeePunchRecordDO.getUserCode());
//            userPunchRecordDTO.setFormId(employeePunchRecordDO.getFormId());
//            String punchTimeString = DateUtil.format(employeePunchRecordDO.getPunchTime(), "yyyy-MM-dd HH:mm");
//            userPunchRecordDTO.setFormatPunchTime(DateUtil.parse(punchTimeString + ":00", "yyyy-MM-dd HH:mm:ss"));
//            userPunchRecordDTOList.add(userPunchRecordDTO);
//        }
//
//        Long tempDayId = beforeDayId;
//        while (tempDayId <= afterDayId) {
//            Long finalTempDayId = tempDayId;
//            //后移一天
//            tempDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(tempDayId.toString()), 1), "yyyyMMdd"));
//            //查询该天的排班
//            List<HrmsAttendanceClassEmployeeConfigDO> dayEmployeeList = employeeDayIdMap.get(finalTempDayId);
//
//            //如果是请假，并且请假单位是天，特殊处理
////            if (companyLeaveConfigDO != null && StringUtils.equalsIgnoreCase(companyLeaveConfigDO.getLeaveUnit(), LeaveUnitEnum.DAYS.getCode())) {
////                if (finalTempDayId.equals(endDayId)) {
////                    continue;
////                }
////            }
//            //当天的结束时间
//            Date finalDateNow = DateUtil.endOfDay(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"));
//            //当天免打卡
//            List<HrmsAttendancePunchConfigRangeDO> noNeedDayPunchConfigRangeDOS = dayPunchConfigRangeDOS.stream()
//                    .filter(item -> item.getEffectTime().compareTo(finalDateNow) < 1 && item.getExpireTime().compareTo(finalDateNow) > -1)
//                    .filter(item -> item.getIsNeedPunch().equals(BusinessConstant.N)).collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(noNeedDayPunchConfigRangeDOS)) {
//                dayDurationNoNeedHandler(beforeDayId, afterDayId, finalTempDayId, startDate, endDate, dayDurationInfoDTOList);
//                continue;
//            }
//
//            //找到循环当天得前一天的排班，需要验证是否跨天
//            Long currentBeforeDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(finalTempDayId.toString()), -1), "yyyyMMdd"));
//            List<HrmsAttendanceClassEmployeeConfigDO> dayBeforeEmployeeList = employeeDayIdMap.get(currentBeforeDayId);
//            List<HrmsAttendancePunchClassItemConfigDO> beforeDayItemList = Lists.newArrayList();
//            if (CollectionUtils.isNotEmpty(dayBeforeEmployeeList) && Objects.nonNull(dayBeforeEmployeeList.get(0).getClassId())) {
//                beforeDayItemList = punchClassItemConfigMap.get(dayBeforeEmployeeList.get(0).getClassId());
//            }
//            //当天么有排班
//            if (CollectionUtils.isEmpty(dayEmployeeList)) {
//                dayDurationNoShiftHandler(beforeDayItemList, beforeDayId, afterDayId, finalTempDayId, startDate, endDate, dayDurationInfoDTOList);
//                continue;
//            }
//
//            //当天有排班
//            //当天是PH/OFF
//            if (dayEmployeeList.get(0).getClassId() == null) {
//                dayDurationPhHandler(beforeDayItemList, beforeDayId, afterDayId, finalTempDayId, startDate, endDate
//                        , dayEmployeeList.get(0).getDayPunchType(), companyLeaveConfigDO, dayDurationInfoDTOList);
//                continue;
//            }
//
//            List<HrmsAttendancePunchConfigDO> dayPunchConfigList = punchConfigMap.get(dayEmployeeList.get(0).getPunchConfigId());
//            if (CollectionUtils.isEmpty(dayPunchConfigList)) {
//                continue;
//            }
//            List<HrmsAttendancePunchClassConfigDO> dayPunchClassList = classConfig.get(dayEmployeeList.get(0).getClassId());
//            if (CollectionUtils.isEmpty(dayPunchClassList)) {
//                continue;
//            }
//            List<HrmsAttendancePunchClassItemConfigDO> dayItemList = punchClassItemConfigMap.get(dayEmployeeList.get(0).getClassId());
//            if (CollectionUtils.isEmpty(dayItemList)) {
//                continue;
//            }
//            dayItemList = dayItemList.stream().sorted(Comparator.comparing(HrmsAttendancePunchClassItemConfigDO::getSortNo)).collect(Collectors.toList());
//            //当天排班是自由打卡规则
//            if (StringUtils.equalsIgnoreCase(dayPunchConfigList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FREE_WORK.name())) {
//                dayDurationFreeShiftHandler(dayPunchClassList.get(0), dayItemList, beforeDayId, afterDayId, finalTempDayId, startDate, endDate, dayDurationInfoDTOList);
//                continue;
//            }
//            //当天排班是一次打卡规则
//            if (StringUtils.equalsIgnoreCase(dayPunchConfigList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FIXED_WORK_ONCE.name())) {
//                dayOnceNormalShiftHandler(dayPunchClassList.get(0), dayItemList, finalTempDayId, startDate, endDate, dayDurationInfoDTOList);
//                continue;
//            }
//            //固定/班次
//            dayDurationNormalShiftHandler(dayPunchClassList.get(0), dayItemList, beforeDayId, afterDayId, finalTempDayId, startDate, endDate, dayDurationInfoDTOList, userPunchRecordDTOList);
//        }
    }

    /**
     * 判断用户处理的异常考勤是否正确(必须是审批中的异常)
     */
    //TODO 判断用户处理的异常考勤是否正确
//    private EmployeeAbnormalAttendanceDO userAbnormalRecordCheck(Long abnormal) {
//        if (abnormal == null) {
//            return null;
//        }
//        List<HrmsEmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = hrmsEmployeeAbnormalAttendanceManage.selectByIdList(Arrays.asList(abnormal));
//        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
//            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
//        }
//        //如果异常已经被别的单据使用了，审批中，报错提示
//        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalAttendanceDOList.get(0);
//        // 如果异常已经处理或过期则提示
//        if (AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED.contains(abnormalAttendanceDO.getStatus())) {
//            throw BusinessException.get(ErrorCodeEnum.ABNORMAL_HAS_BEEN_HANDLER.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ABNORMAL_HAS_BEEN_HANDLER.getDesc()));
//        }
//        if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getStatus(), AbnormalAttendanceStatusEnum.IN_REVIEW.getCode())) {
//            throw BusinessException.get(ErrorCodeEnum.ABNORMAL_IN_REVIEW.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ABNORMAL_IN_REVIEW.getDesc()));
//        }
//        return abnormalAttendanceDO;
//    }

    /**
     * 构建用户请假记录数据
     *
     * @param param  入参
     * @param remark 备注
     * @param type   请假类型
     */
    public UserLeaveRecordDO buildUserLeaveRecord(BigDecimal totalLeaveTime,
                                                  LeaveAddParam param,
                                                  String type,
                                                  String remark) {
        UserLeaveRecordDO userLeaveRecord = new UserLeaveRecordDO();
        userLeaveRecord.setId(defaultIdWorker.nextId());
        userLeaveRecord.setUserId(param.getUserId());
        userLeaveRecord.setUserCode(param.getUserCode());
        userLeaveRecord.setDate(new Date());
        userLeaveRecord.setDayId(Long.parseLong(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN)));
        userLeaveRecord.setConfigId(param.getConfigId());
        userLeaveRecord.setLeaveName(param.getLeaveName());
        userLeaveRecord.setLeaveType(param.getLeaveType());
        userLeaveRecord.setType(type);
        userLeaveRecord.setLeaveStartDay(param.getLeaveStartDate());
        userLeaveRecord.setLeaveEndDay(param.getLeaveEndDate());
        userLeaveRecord.setLeaveMinutes(totalLeaveTime);
        userLeaveRecord.setRemark(remark);
        fillDOInsert(userLeaveRecord);
        userLeaveRecord.setOperationUserCode(RequestInfoHolder.getUserCode());
        userLeaveRecord.setOperationUserName(RequestInfoHolder.getUserName());
        return userLeaveRecord;
    }

    /**
     * 构建假期详情表数据
     *
     * @param userLeaveStageDetailInfoList 需要修改的假期详情数据
     * @param param                        入参
     * @param totalLeaveTime               请假总时长
     */
    public void buildUserLeaveStageDetailList(List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList,
                                              LeaveAddParam param,
                                              BigDecimal totalLeaveTime) {
        UserLeaveDetailQuery query = UserLeaveDetailQuery.builder()
                .userId(param.getUserId())
                .configId(param.getConfigId())
                .build();
        List<UserLeaveDetailDO> userLeaveDetail = userLeaveDetailService.selectUserLeaveDetail(query);
        if (CollUtil.isEmpty(userLeaveDetail)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.USER_NOT_HAVE_THIS_LEAVE_TYPE);
        }
        List<Long> leaveIdList = userLeaveDetail.stream().map(UserLeaveDetailDO::getId).collect(Collectors.toList());
        List<UserLeaveStageDetailDO> userLeaveStageDetailList = userLeaveStageDetailService.selectByLeaveId(leaveIdList);
        if (CollUtil.isEmpty(userLeaveStageDetailList)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.THE_USER_LACKS_DETAILED_DATA_FOR_THE_LEAVE_TYPE);
        }
        // 先扣除结转的假期，不够的时候再扣除非结转的假期余额,将假期先按照是否结转倒叙排序后再按照阶段排序
        // 将假期按照阶段倒序，优先使用比率高的假期余额
        List<UserLeaveStageDetailDO> reversedUserLeaveStageDetailList = userLeaveStageDetailList.stream()
                .sorted(Comparator.comparingInt(UserLeaveStageDetailDO::getLeaveMark)
                        .thenComparing(UserLeaveStageDetailDO::getPercentSalary).reversed())
                .collect(Collectors.toList());
        // 这边无需考虑假期余额不够扣减的情况，因为如果假期余额不够扣减，前面校验都不会通过的
        for (int i = 0; i < reversedUserLeaveStageDetailList.size(); i++) {
            UserLeaveStageDetailDO stageDetailInfo = reversedUserLeaveStageDetailList.get(i);
            // 不是最后一个阶梯，并且有假期<=0
            // 当前逻辑 ：1. 不是最后一个阶梯，判断假期余额是否小于等于0，如果小于等于0则跳过该假期阶梯。2. 如果是最后一个阶梯，直接使用最后一个阶梯
            //if (i != reversedUserLeaveStageDetailList.size() - 1 && stageDetailInfo.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) < 1) {
            //    continue;
            //}

            // 如果当前阶段假期余额 <= 0，则表示无法扣减当前阶段假期余额
            if (stageDetailInfo.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            /*
                当前阶段(请假时长 - 假期余额)
                    大于0:该阶段假期不足以请假，需要加上下一个阶段的假期余额
                    等于0:正好完成请该阶段的假期
                    小于0:可以完全请该阶段的假期
             */
            BigDecimal difference = totalLeaveTime.subtract(stageDetailInfo.getLeaveResidueMinutes());
            if (difference.compareTo(BigDecimal.ZERO) <= 0) {
                stageDetailInfo.setLeaveUsedMinutes(stageDetailInfo.getLeaveUsedMinutes().add(totalLeaveTime));
                stageDetailInfo.setLeaveResidueMinutes(stageDetailInfo.getLeaveResidueMinutes().subtract(totalLeaveTime));
                stageDetailInfo.setLastUpdDate(new Date());
                stageDetailInfo.setLastUpdUserCode(RequestInfoHolder.getUserCode());
                stageDetailInfo.setLastUpdUserName(RequestInfoHolder.getUserName());
                userLeaveStageDetailInfoList.add(stageDetailInfo);
                break;
            }
            // 提前存储假期剩余时间，为后面减法做准备
            BigDecimal subtractLeaveResidueMinutes = stageDetailInfo.getLeaveResidueMinutes();
            // 大于0：直接该阶段假期余额变成0，已使用 = 已使用 + 假期余额
            stageDetailInfo.setLeaveUsedMinutes(stageDetailInfo.getLeaveUsedMinutes().add(stageDetailInfo.getLeaveResidueMinutes()));
            stageDetailInfo.setLeaveResidueMinutes(BigDecimal.ZERO);
            stageDetailInfo.setLastUpdDate(new Date());
            stageDetailInfo.setLastUpdUserCode(RequestInfoHolder.getUserCode());
            stageDetailInfo.setLastUpdUserName(RequestInfoHolder.getUserName());
            userLeaveStageDetailInfoList.add(stageDetailInfo);
            // 更新请假时间，为后续循环准备
            totalLeaveTime = totalLeaveTime.subtract(subtractLeaveResidueMinutes);
        }
    }


}
