package com.imile.attendance.form.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-21
 * @version: 1.0
 */
@Data
public class DurationDetailParam {
    /**
     * 特殊逻辑
     */
    private Long formId;

    /**
     * 被申请人ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;

    /**
     * 被申请人国家
     */
    private String country;

    /**
     * 假期规则主键
     */
    private Long configId;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 请假/外勤开始时间
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    /**
     * 请假/外勤结束时间
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;
}
