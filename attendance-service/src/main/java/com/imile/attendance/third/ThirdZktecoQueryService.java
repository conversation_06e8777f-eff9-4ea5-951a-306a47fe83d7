package com.imile.attendance.third;

import com.imile.attendance.infrastructure.repository.third.model.ZktecoAreaSnRelationDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
@Service
public class ThirdZktecoQueryService {

    @Resource
    private ZktecoAreaSnRelationManage zktecoAreaSnRelationManage;


    /**
     * 通过dept_id查询所有包含的区域
     */
    public List<ZktecoAreaSnRelationDO> selectByDeptId(Long deptId) {
        if (deptId == null) {
            return Collections.emptyList();
        }
        List<ZktecoAreaSnRelationDO> relationDOList = zktecoAreaSnRelationManage.selectByDeptId(deptId);
        // 使用流操作和新增的listDeptIdList方法进行精确过滤
        return relationDOList.stream()
                .filter(relationDO -> relationDO.listDeptIdList().contains(deptId))
                .collect(Collectors.toList());
    }

    /**
     * 通过user_id查询所有包含的区域
     */
    public List<ZktecoAreaSnRelationDO> selectByUserId(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        List<ZktecoAreaSnRelationDO> relationDOList = zktecoAreaSnRelationManage.selectByUserId(userId);
        return relationDOList.stream()
                .filter(relationDO -> relationDO.listUserIdList().contains(userId))
                .collect(Collectors.toList());
    }
}
