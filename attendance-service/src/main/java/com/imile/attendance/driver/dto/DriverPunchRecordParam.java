package com.imile.attendance.driver.dto;

import com.imile.attendance.query.ResourceQuery;
import com.imile.common.constant.ValidCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * {@code @author:} allen
 * {@code @className:} DriverPunchRecordParam
 * {@code @since:} 2024-01-22 10:35
 * {@code @description:}
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description="司机打卡记录详情入参")
public class DriverPunchRecordParam extends ResourceQuery {
    /**
     * 司机账号
     */
    @ApiModelProperty(value="司机账号")
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String userCode;

    /**
     * day_id 示例：20240124
     */
    @ApiModelProperty(value="day_id 示例：20240124")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long dayId;
}
